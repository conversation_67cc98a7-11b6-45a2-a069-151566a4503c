# Epic: Campanhas e Disparo de Mensagens via WhatsApp - Melhorias e Expansão

## Overview

Esta Epic visa aprimorar significativamente o sistema de campanhas WhatsApp existente, transformando-o em uma plataforma robusta e completa para marketing digital. O sistema atual já possui funcionalidades básicas de criação, lançamento e envio de campanhas, mas necessita de melhorias substanciais em organização, monitoramento, reenvio e análise de performance.

### Funcionalidades Atuais Identificadas:
- ✅ Criação e gestão básica de campanhas (`Campaign` model)
- ✅ Sistema de templates com publicação no WhatsApp
- ✅ Geração automática de mensagens por campanha
- ✅ Envio automatizado via cron job (`whatsapp:send-messages`)
- ✅ Integração com WhatsApp Business API
- ✅ Webhook para recebimento de status de entrega
- ✅ Sistema básico de status de mensagens (`MessageStatus` enum)

### Melhorias Propostas:

#### 1. **Sistema de Categorização e Tags**
Implementar um sistema robusto de organização de campanhas através de categorias e tags, permitindo melhor segmentação e organização para empresas com alto volume de campanhas.

#### 2. **Aprimoramento do Sistema de Status**
Expandir o `MessageStatus` enum atual e criar um `CampaignStatus` enum para melhor rastreamento do ciclo de vida das campanhas, incluindo estados como: rascunho, agendada, em execução, pausada, concluída, cancelada, com falhas.

#### 3. **Funcionalidades Avançadas de Mensagens**
Desenvolver endpoints específicos para gestão granular de mensagens, incluindo listagem por campanha, reenvio de mensagens falhadas, envio individual e consulta de status em tempo real.

#### 4. **Monitoramento e Sincronização Aprimorados**
Implementar sistema de sincronização bidirecional com WhatsApp, incluindo consulta ativa de status de mensagens e job automatizado para atualização de mensagens falhadas.

#### 5. **Analytics e Métricas Avançadas**
Criar sistema completo de métricas e relatórios, incluindo taxa de entrega, tempo de resposta, engajamento e ROI por campanha.

## Plano de Implementação

### 1. Sistema de Categorização e Tags

#### Rotas/Endpoints Necessários:
- `POST /api/campaigns/categories` - Criar categoria de campanha
- `GET /api/campaigns/categories` - Listar categorias
- `POST /api/campaigns/{id}/tags` - Adicionar tags à campanha
- `GET /api/campaigns/tags` - Listar todas as tags disponíveis
- `GET /api/campaigns?category_id={id}&tags={tag1,tag2}` - Filtrar campanhas por categoria/tags

#### Database:
- **Tabela `campaign_categories`**: Necessária para organizar campanhas em categorias como "Promocional", "Informativo", "Suporte", "Sazonal". Campos: id, name, description, color, organization_id, created_at, updated_at.
- **Tabela `campaign_tags`**: Sistema de tags flexível para marcação livre. Campos: id, name, organization_id, usage_count, created_at, updated_at.
- **Tabela `campaign_tag_assignments`**: Relacionamento many-to-many entre campanhas e tags. Campos: campaign_id, tag_id, assigned_at.
- **Migração para `campaigns`**: Adicionar campo `category_id` na tabela campaigns existente.

#### Domínios:
- **CampaignCategory**: Gerenciar categorias com validação de nome único por organização
- **CampaignTag**: Gerenciar tags com auto-complete e sugestões baseadas em uso
- **CampaignTagAssignment**: Relacionamento entre campanhas e tags

#### Usecases:
- **CreateCampaignCategory**: Criar categoria com validação de duplicatas
- **AssignTagsToCampaign**: Atribuir múltiplas tags com validação
- **GetCampaignsByFilters**: Busca avançada por categoria, tags e outros filtros

### 2. Aprimoramento do Sistema de Status

#### Enums Necessários:
- **CampaignStatus**: Expandir além dos booleans atuais (is_sent, is_sending, is_scheduled) para enum robusto:
  - `DRAFT` (1) - Campanha em criação
  - `SCHEDULED` (2) - Agendada para envio futuro
  - `QUEUED` (3) - Na fila para processamento
  - `SENDING` (4) - Enviando mensagens
  - `COMPLETED` (5) - Envio concluído com sucesso
  - `PAUSED` (6) - Pausada pelo usuário
  - `FAILED` (7) - Falhou durante o envio
  - `CANCELLED` (8) - Cancelada antes do envio

#### Rotas/Endpoints:
- `PATCH /api/campaigns/{id}/status` - Alterar status da campanha (pausar, cancelar, retomar)
- `GET /api/campaigns/{id}/status-history` - Histórico de mudanças de status
- `POST /api/campaigns/{id}/pause` - Pausar campanha em execução
- `POST /api/campaigns/{id}/resume` - Retomar campanha pausada

#### Database:
- **Tabela `campaign_status_history`**: Rastrear mudanças de status. Campos: id, campaign_id, old_status, new_status, reason, user_id, created_at.
- **Migração para `campaigns`**: Substituir campos booleanos por campo `status` enum e adicionar `paused_at`, `cancelled_at`, `failed_at`.

#### Usecases:
- **ChangeCampaignStatus**: Alterar status com validação de transições válidas
- **PauseCampaign**: Pausar envio e marcar mensagens pendentes
- **ResumeCampaign**: Retomar envio de mensagens pausadas

### 3. Funcionalidades Avançadas de Mensagens

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/{id}/messages` - Listar mensagens de uma campanha específica
- `POST /api/campaigns/{id}/messages/resend-failed` - Reenviar apenas mensagens falhadas
- `POST /api/messages/{id}/resend` - Reenviar mensagem individual
- `GET /api/messages/{id}/status` - Consultar status detalhado de uma mensagem
- `POST /api/messages/send-individual` - Enviar mensagem individual fora de campanha
- `GET /api/messages/failed` - Listar todas as mensagens falhadas da organização
- `POST /api/messages/bulk-resend` - Reenvio em lote com filtros

#### Database:
- **Tabela `message_delivery_attempts`**: Rastrear tentativas de envio. Campos: id, message_id, attempt_number, status, error_message, attempted_at, whatsapp_response.
- **Migração para `messages`**: Adicionar campos `delivery_attempts`, `last_attempt_at`, `next_retry_at`, `max_retries`.

#### Domínios:
- **MessageDeliveryAttempt**: Gerenciar tentativas de entrega
- **MessageRetryPolicy**: Política de reenvio com backoff exponencial
- **BulkMessageOperation**: Operações em lote para mensagens

#### Usecases:
- **GetMessagesByCampaign**: Listar mensagens com filtros avançados (status, cliente, data)
- **ResendFailedMessages**: Reenviar mensagens falhadas com política de retry
- **SendIndividualMessage**: Enviar mensagem única sem campanha
- **BulkResendMessages**: Reenvio em lote com validações
- **GetMessageDeliveryStatus**: Status detalhado com histórico de tentativas

### 4. Monitoramento e Sincronização Aprimorados

#### Rotas/Endpoints:
- `POST /api/whatsapp/sync-message-status/{id}` - Sincronizar status de mensagem específica
- `POST /api/whatsapp/sync-campaign-status/{id}` - Sincronizar status de toda campanha
- `GET /api/whatsapp/delivery-reports` - Relatórios de entrega consolidados
- `POST /api/whatsapp/webhook/status-update` - Webhook melhorado para status

#### Database:
- **Tabela `whatsapp_sync_logs`**: Log de sincronizações. Campos: id, sync_type, entity_id, status, response_data, synced_at.
- **Migração para `whatsapp_messages`**: Adicionar campos `last_status_check`, `status_check_count`, `delivery_confirmed_at`.

#### Jobs/Cron Necessários:
- **SyncFailedMessagesStatus**: Job que roda a cada 5 minutos para consultar status de mensagens falhadas no WhatsApp e atualizar localmente
- **UpdateCampaignStatusFromMessages**: Job que analisa status das mensagens e atualiza status da campanha automaticamente
- **CleanupOldDeliveryAttempts**: Job de limpeza para remover tentativas antigas

#### Usecases:
- **SyncMessageStatusWithWhatsApp**: Consultar API do WhatsApp para status real
- **UpdateMessageFromWebhook**: Processar webhook com validação aprimorada
- **ReconcileMessageStatuses**: Reconciliar diferenças entre local e WhatsApp
- **GenerateDeliveryReport**: Relatório consolidado de entregas

### 5. Analytics e Métricas Avançadas

#### Rotas/Endpoints:
- `GET /api/campaigns/{id}/analytics` - Analytics detalhadas da campanha
- `GET /api/campaigns/analytics/summary` - Resumo de todas as campanhas
- `GET /api/campaigns/analytics/comparison` - Comparar performance entre campanhas
- `POST /api/campaigns/analytics/export` - Exportar relatórios em PDF/Excel
- `GET /api/analytics/dashboard` - Dashboard consolidado de métricas

#### Database:
- **Tabela `campaign_analytics`**: Métricas calculadas. Campos: id, campaign_id, total_sent, delivered, failed, read_rate, response_rate, calculated_at.
- **Tabela `campaign_performance_snapshots`**: Snapshots diários de performance. Campos: id, campaign_id, date, metrics_json, created_at.
- **Tabela `message_engagement_events`**: Eventos de engajamento. Campos: id, message_id, event_type, occurred_at, metadata_json.

#### Domínios:
- **CampaignAnalytics**: Métricas e cálculos de performance
- **PerformanceSnapshot**: Snapshot temporal de métricas
- **EngagementEvent**: Eventos de interação com mensagens

#### Usecases:
- **CalculateCampaignMetrics**: Calcular métricas em tempo real
- **GeneratePerformanceReport**: Relatórios detalhados com gráficos
- **CompareCampaignPerformance**: Análise comparativa
- **ExportAnalyticsReport**: Exportação em múltiplos formatos

## Melhorias Específicas Identificadas

### Problema 1: Sistema de Status Limitado
**Situação Atual**: O sistema usa campos booleanos (`is_sent`, `is_sending`, `is_scheduled`) que não cobrem todos os estados possíveis de uma campanha.
**Solução**: Implementar `CampaignStatus` enum com estados granulares e histórico de mudanças.

### Problema 2: Falta de Reenvio Inteligente
**Situação Atual**: Não existe funcionalidade para reenviar apenas mensagens falhadas de uma campanha.
**Solução**: Endpoints específicos para reenvio com políticas de retry e backoff exponencial.

### Problema 3: Monitoramento Passivo
**Situação Atual**: Sistema depende apenas de webhooks para atualização de status.
**Solução**: Jobs ativos que consultam API do WhatsApp para sincronizar status de mensagens falhadas.

### Problema 4: Falta de Organização
**Situação Atual**: Campanhas não possuem categorização ou tags para organização.
**Solução**: Sistema completo de categorias e tags com filtros avançados.

### Problema 5: Analytics Limitadas
**Situação Atual**: Não existe sistema de métricas e relatórios detalhados.
**Solução**: Dashboard completo com métricas de entrega, engajamento e ROI.

## Plano de Testes

### Testes Unitários:
- Validação de transições de status de campanha
- Políticas de retry para mensagens falhadas
- Cálculos de métricas e analytics
- Validação de categorias e tags

### Testes de Integração:
- Sincronização com WhatsApp Business API
- Processamento de webhooks de status
- Jobs de sincronização automática
- Exportação de relatórios

### Testes de Performance:
- Envio de campanhas com 10k+ mensagens
- Processamento de webhooks em massa
- Consultas de analytics com grandes volumes
- Jobs de sincronização com timeout

### Testes de Regressão:
- Compatibilidade com sistema atual de campanhas
- Migração de dados existentes
- Funcionalidades existentes não afetadas

## Conclusão

Esta Epic transformará o sistema atual de campanhas WhatsApp em uma plataforma robusta e profissional, adequada para empresas de todos os tamanhos. As melhorias propostas abordam limitações críticas identificadas no sistema atual e introduzem funcionalidades essenciais para um sistema de marketing digital moderno.

### Benefícios Esperados:
- **Organização**: Sistema de categorias e tags para melhor gestão
- **Confiabilidade**: Monitoramento ativo e reenvio inteligente
- **Visibilidade**: Analytics detalhadas e relatórios profissionais
- **Controle**: Gestão granular de status e operações
- **Escalabilidade**: Arquitetura preparada para alto volume

### Impacto no Negócio:
- Redução de mensagens perdidas através de reenvio automático
- Melhor ROI através de analytics detalhadas
- Maior satisfação do cliente com entregas confiáveis
- Eficiência operacional através de melhor organização

## Referências

- WhatsApp Business API Documentation
- Meta for Developers - Webhook Guide
- Laravel Queue Documentation
- Campaign Management Best Practices
- Marketing Analytics Standards

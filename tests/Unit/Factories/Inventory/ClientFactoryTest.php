<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\ClientFactory;
use App\Domains\Inventory\Client;
use App\Models\Client as ClientModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ClientFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Client::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->phone, $domain->phone);
        $this->assertEquals($model->email, $domain->email);
        $this->assertEquals($model->profession, $domain->profession);
        $this->assertEquals($model->birthdate, $domain->birthdate);
        $this->assertEquals($model->cpf, $domain->cpf);
        $this->assertEquals($model->cnpj, $domain->cnpj);
        $this->assertEquals($model->service, $domain->service);
        $this->assertEquals($model->address, $domain->address);
        $this->assertEquals($model->number, $domain->number);
        $this->assertEquals($model->neighborhood, $domain->neighborhood);
        $this->assertEquals($model->cep, $domain->cep);
        $this->assertEquals($model->complement, $domain->complement);
        $this->assertEquals($model->civil_state, $domain->civil_state);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_store_request()
    {
        // ClientFactory requires specific StoreRequest class
        $this->markTestSkipped('Store request test requires specific StoreRequest class implementation');
    }

    public function test_build_from_update_request()
    {
        // ClientFactory requires specific UpdateRequest class
        $this->markTestSkipped('Update request test requires specific UpdateRequest class implementation');
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(ClientFactory::class);

        $this->assertInstanceOf(ClientFactory::class, $factory);
    }

    public function test_factory_can_handle_different_client_types()
    {
        $individualClient = ClientModel::factory()->individual()->create();
        $companyClient = ClientModel::factory()->company()->create();
        $clientWithAddress = ClientModel::factory()->withCompleteAddress()->create();
        $minimalClient = ClientModel::factory()->minimal()->create();

        $factory = $this->createFactoryInstance();

        $individualDomain = $factory->buildFromModel($individualClient);
        $companyDomain = $factory->buildFromModel($companyClient);
        $addressDomain = $factory->buildFromModel($clientWithAddress);
        $minimalDomain = $factory->buildFromModel($minimalClient);

        $this->assertInstanceOf(Client::class, $individualDomain);
        $this->assertNotNull($individualDomain->cpf);
        $this->assertNull($individualDomain->cnpj);

        $this->assertInstanceOf(Client::class, $companyDomain);
        $this->assertNull($companyDomain->cpf);
        $this->assertNotNull($companyDomain->cnpj);

        $this->assertInstanceOf(Client::class, $addressDomain);
        $this->assertNotNull($addressDomain->address);
        $this->assertNotNull($addressDomain->cep);

        $this->assertInstanceOf(Client::class, $minimalDomain);
        $this->assertNotNull($minimalDomain->phone);
    }

    protected function createFactoryInstance()
    {
        return new ClientFactory();
    }

    protected function getDomainClass(): string
    {
        return Client::class;
    }

    protected function createModelInstance()
    {
        return ClientModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        return new class {
            public $organization_id = 1;
            public $name = 'John Doe';
            public $phone = '+1234567890';
            public $email = '<EMAIL>';
            public $profession = 'Engineer';
            public $birthdate = '1990-01-01';
            public $cpf = '12345678901';
            public $cnpj = null;
            public $service = 'Software Development';
            public $address = '123 Main St';
            public $number = '123';
            public $neighborhood = 'Downtown';
            public $cep = '12345-678';
            public $complement = 'Apt 1';
            public $civil_state = 'single';
            public $description = 'Test client';
        };
    }

    protected function createUpdateRequest()
    {
        return new class {
            public $name = 'Jane Smith';
            public $phone = '+0987654321';
            public $email = '<EMAIL>';
            public $profession = 'Designer';
            public $birthdate = '1985-05-15';
            public $cpf = '98765432109';
            public $cnpj = null;
            public $service = 'Graphic Design';
            public $address = '456 Oak Ave';
            public $number = '456';
            public $neighborhood = 'Uptown';
            public $cep = '98765-432';
            public $complement = 'Suite 2';
            public $civil_state = 'married';
            public $description = 'Updated client';
        };
    }
}

<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\ProductFactory;
use App\Factories\Inventory\BrandFactory;
use App\Domains\Inventory\Product;
use App\Models\Product as ProductModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->brand_id, $domain->brand_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->barcode, $domain->barcode);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->price, $domain->price);
        $this->assertEquals($model->unity, $domain->unity);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_array()
    {
        $models = collect([
            $this->createModelInstance(),
            $this->createModelInstance(),
            $this->createModelInstance(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Product::class, $domain);
        }
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(ProductFactory::class);

        $this->assertInstanceOf(ProductFactory::class, $factory);
    }

    public function test_factory_can_handle_different_product_types()
    {
        $expensiveProduct = ProductModel::factory()->expensive()->create();
        $cheapProduct = ProductModel::factory()->cheap()->create();
        $productWithoutBrand = ProductModel::factory()->withoutBrand()->create();
        $recentlyPricedProduct = ProductModel::factory()->recentlyPriced()->create();

        $factory = $this->createFactoryInstance();

        $expensiveDomain = $factory->buildFromModel($expensiveProduct);
        $cheapDomain = $factory->buildFromModel($cheapProduct);
        $noBrandDomain = $factory->buildFromModel($productWithoutBrand);
        $recentDomain = $factory->buildFromModel($recentlyPricedProduct);

        $this->assertInstanceOf(Product::class, $expensiveDomain);
        $this->assertGreaterThanOrEqual(1000, $expensiveDomain->price);

        $this->assertInstanceOf(Product::class, $cheapDomain);
        $this->assertLessThanOrEqual(50, $cheapDomain->price);

        $this->assertInstanceOf(Product::class, $noBrandDomain);
        $this->assertNull($noBrandDomain->brand_id);

        $this->assertInstanceOf(Product::class, $recentDomain);
        $this->assertNotNull($recentDomain->last_priced_at);
    }

    public function test_build_from_store_request()
    {
        // ProductFactory doesn't have buildFromStoreRequest method
        $this->markTestSkipped('ProductFactory does not implement buildFromStoreRequest');
    }

    public function test_build_from_update_request()
    {
        // ProductFactory doesn't have buildFromUpdateRequest method
        $this->markTestSkipped('ProductFactory does not implement buildFromUpdateRequest');
    }

    protected function createFactoryInstance()
    {
        return new ProductFactory(new BrandFactory());
    }

    protected function getDomainClass(): string
    {
        return Product::class;
    }

    protected function createModelInstance()
    {
        return ProductModel::factory()->create();
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Project extends Model
{
    use SoftDeletes;

    protected $table = 'projects';

    protected $fillable = [
        'organization_id',
        'client_id',
        'budget_id',
        'name',
        'description',
        'value',
        'cost',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function client(){
        return $this->belongsTo(Client::class);
    }
    public function budget(){
        return $this->belongsTo(Budget::class);
    }

    public function stock_entries(){
        return $this->hasMany(StockEntry::class);
    }
    public function stock_exits(){
        return $this->hasMany(StockExit::class);
    }
    public function custom_products(){
        return $this->hasMany(CustomProduct::class);
    }
    public function products(){
        return $this->belongsToMany(Product::class, 'projects_products')->withPivot([
            'id',
            'quantity',
            'value',
            'description',
        ]);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Step extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'steps';

    protected $fillable = [
        'organization_id',
        'flow_id',
        'step',
        'type',
        'position',
        'next_step',
        'earlier_step',
        'is_initial_step',
        'is_ending_step',
        'is_message',
        'is_interactive',
        'is_command',
        'is_input',
        'json',
        'input',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function flow(){
        return $this->belongsTo(Flow::class);
    }

    public function component(){
        return $this->hasOne(Component::class);
    }
}

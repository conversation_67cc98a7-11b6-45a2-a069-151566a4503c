<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'profile_id',
        'organization_id',
        'first_name',
        'last_name',
        'email',
        'username',
        'phone',
        'cpf',
        'password',
        'remember_token',
        'email_verified_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function profile(){
        return $this->belongsTo(Profile::class);
    }
    public function stock_entries(){
        return $this->hasMany(StockEntry::class);
    }
    public function stock_exits(){
        return $this->hasMany(StockExit::class);
    }
    public function histories(){
        return $this->hasMany(ProductHistory::class);
    }
    public function sales(){
        return $this->hasMany(Sale::class);
    }
}

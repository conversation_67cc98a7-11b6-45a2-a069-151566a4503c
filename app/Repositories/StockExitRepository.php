<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockExitFilters;
use App\Domains\Filters\StockExitReportFilters;
use App\Domains\Inventory\Report\StockExitReport;
use App\Domains\Inventory\StockExit as StockExitDomain;
use App\Factories\Inventory\StockExitFactory;
use App\Factories\Report\StockExitReportFactory;
use App\Models\StockExit;
use EloquentBuilder;

class StockExitRepository
{
    private StockExitFactory $stockExitFactory;
    private StockExitReportFactory $stockExitReportFactory;

    public function __construct(StockExitFactory $stockExitFactory, StockExitReportFactory $stockExitReportFactory){
        $this->stockExitFactory = $stockExitFactory;
        $this->stockExitReportFactory = $stockExitReportFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(StockExitFilters $filters, OrderBy $orderBy) : array {
        $stockExits = [];

        $models =  EloquentBuilder::to(StockExit::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $stockExits[] = $this->stockExitFactory->buildFromModel($model);
        }

        return [
            'data' => $stockExits,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, StockExitFilters $filters, OrderBy $orderBy) : array {
        $stockExits = [];

        $models =  EloquentBuilder::to(StockExit::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $stockExits[] = $this->stockExitFactory->buildFromModel($model);
        }

        return [
            'data' => $stockExits,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, StockExitFilters $filters): int {
        return EloquentBuilder::to(StockExit::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, StockExitFilters $filters, string $column): float|int {
        return EloquentBuilder::to(StockExit::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(StockExitDomain $stockExit) : StockExitDomain {
        $savedStockExit = StockExit::create($stockExit->toStoreArray());

        $stockExit->id = $savedStockExit->id;

        return $stockExit;
    }

    public function update(StockExitDomain $stockExit, int $organization_id) : StockExitDomain {
        StockExit::where('id', $stockExit->id)
            ->where('organization_id', $organization_id)
            ->update($stockExit->toUpdateArray());

        return $stockExit;
    }

    public function fetchById(int $id) : StockExitDomain {
        return $this->stockExitFactory->buildFromModel(
            StockExit::with('user')
                ->with('product')
                ->with('project')
                ->findOrFail($id)
        );
    }

    public function delete(StockExitDomain $stockExit) : bool {
        return StockExit::find($stockExit->id)->delete();
    }

    /**
     * @param int $organization_id
     * @param StockExitReportFilters $filters
     * @param OrderBy $orderBy
     * @return StockExitReport
     */
    public function fetchReport(
        int $organization_id,
        StockExitReportFilters $filters,
        OrderBy $orderBy,
        ?string $grouped_by
    ) : StockExitReport {
        $models = EloquentBuilder::to(StockExit::class, $filters->filters)
            ->with("product")
            ->with("project")
            ->with("client")
            ->with("user")
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->get();

        return $this->stockExitReportFactory->buildFromModels($models, $grouped_by);
    }
}

<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TemplateFilters;
use App\Domains\ChatBot\Template as TemplateDomain;
use App\Factories\ChatBot\TemplateFactory;
use App\Models\Template;
use EloquentBuilder;

class TemplateRepository
{
    private TemplateFactory $templateFactory;

    public function __construct(TemplateFactory $templateFactory){
        $this->templateFactory = $templateFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(TemplateFilters $filters, OrderBy $orderBy) : array {
        $templates = [];

        $models = EloquentBuilder::to(Template::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $templates[] = $this->templateFactory->buildFromModel($model);
        }

        return [
            'data' => $templates,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, TemplateFilters $filters, OrderBy $orderBy) : array {
        $templates = [];

        $models = EloquentBuilder::to(Template::class, $filters->filters)
            ->with('components')
            ->with('whatsAppTemplate')
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $templates[] = $this->templateFactory->buildFromModel($model);
        }

        return [
            'data' => $templates,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, TemplateFilters $filters): int {
        return EloquentBuilder::to(Template::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, TemplateFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Template::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(TemplateDomain $template) : TemplateDomain {
        $savedtemplate = Template::create($template->toStoreArray());

        $template->id = $savedtemplate->id;

        return $template;
    }

    public function update(TemplateDomain $template, int $organization_id) : TemplateDomain {
        Template::where('id', $template->id)
            ->where('organization_id', $organization_id)
            ->update($template->toUpdateArray());

        return $template;
    }

    public function save(TemplateDomain $template, int $organization_id) : TemplateDomain {
        if ($template->id){
            $this->update($template, $organization_id);
        }
        return $this->store($template);
    }

    public function fetchById(int $id) : ?TemplateDomain {
        return $this->templateFactory->buildFromModel(
            Template::findOrFail($id)
        );
    }

    public function delete(TemplateDomain $template) : bool {
        return Template::find($template->id)->delete();
    }
}

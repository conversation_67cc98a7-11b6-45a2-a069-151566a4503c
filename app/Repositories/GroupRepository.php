<?php

namespace App\Repositories;

use App\Domains\Filters\GroupFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Group as GroupDomain;
use App\Factories\Inventory\GroupFactory;
use App\Models\Group;
use EloquentBuilder;

class GroupRepository
{
    private GroupFactory $groupFactory;

    public function __construct(GroupFactory $groupFactory){
        $this->groupFactory = $groupFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(GroupFilters $filters, OrderBy $orderBy) : array {
        $groups = [];

        $models = EloquentBuilder::to(Group::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);


        foreach ($models as $model){
            $groups[] = $this->groupFactory->buildFromModel($model);
        }

        return [
            'data' => $groups,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, GroupFilters $filters, OrderBy $orderBy) : array {
        $groups = [];

        $models = EloquentBuilder::to(Group::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $groups[] = $this->groupFactory->buildFromModel($model);
        }

        return [
            'data' => $groups,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, GroupFilters $filters): int {
        return EloquentBuilder::to(Group::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, GroupFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Group::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(GroupDomain $group) : GroupDomain {
        $savedGroup = Group::create($group->toStoreArray());

        $group->id = $savedGroup->id;

        return $group;
    }

    public function update(GroupDomain $group, int $organization_id) : GroupDomain {
        Group::where('id', $group->id)
            ->where('organization_id', $organization_id)
            ->update($group->toUpdateArray());

        return $group;
    }

    public function fetchById(int $id) : GroupDomain {
        return $this->groupFactory->buildFromModel(
            Group::findOrFail($id)
        );
    }

    public function delete(GroupDomain $group) : bool {
        return Group::find($group->id)->delete();
    }
}

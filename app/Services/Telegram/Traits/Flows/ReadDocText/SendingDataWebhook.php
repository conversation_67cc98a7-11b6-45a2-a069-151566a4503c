<?php

namespace App\Services\Telegram\Traits\Flows\ReadDocText;

use App\Helpers\DBLog;
use App\Services\GoogleVision\GoogleVisionOCR;
use App\Services\Telegram\Domains\Messages\DocumentReader\IsDataCorrect;
use App\Services\Telegram\UseCases\Flows\FinishFlow;
use App\Services\Telegram\UseCases\Flows\ReadDocText\SendDocumentToWebhook;
use App\Services\Telegram\UseCases\TelegramChat\UpdateStatus;
use Illuminate\Support\Facades\Log;

trait SendingDataWebhook
{
    //private const array IN_APPROVAL_ARRAY = ["sim", "s", "y", "yes"];

    public function sendingDataWebhook()
    {
        Log::info("SendingDataWebhook::sendingDataWebhook");
        $response = !empty($this->telegram->callbackData) ? $this->telegram->callbackData : $this->telegram->text;

        DBLog::log(
            "SendingDataWebhook::sendingDataWebhook",
            "Telegram",
            $this->telegram->telegramChat->organization_id,
            $this->telegram->telegramChat->user_id,
            ["callback_data" => $response]
        );

        if($response !== 'n'){
            Log::info('Array: '.is_array(json_decode($this->telegram->telegramChat->ocr_data)) ? 'sim' : 'nao');
            Log::info($response);
            /** @var SendDocumentToWebhook $useCaseSend */
            $useCaseSend = app()->makeWith(SendDocumentToWebhook::class, ["telegram" => $this->telegram]);
            $useCaseSend->perform($response === 's' ? $this->telegram->telegramChat->ocr_data : json_decode($response, true));
    
            $this->telegram->sendMessage("Dados enviados com sucesso!");
    
            /** @var FinishFlow $useCase */
            $useCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
            $useCase->perform(false);
    
            return;
        }else{
            $this->telegram->sendMessage("Desculpe pelo erro, por favor tente novamente!");

            /** @var FinishFlow $useCase */
            $useCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
            $useCase->perform();
        }
    }
}

<?php

namespace App\Services\Telegram\Domains;

use App\Domains\User;
use App\Domains\Inventory\Client;

class TelegramUser
{
    public ?int $id;
    public ?int $user_id;
    public ?int $client_id;
    public ?int $telegram_id;
    public ?string $description;

    public ?User $user;
    public ?Client $client;

    public function __construct(
        ?int $id,
        ?int $user_id,
        ?int $client_id,
        ?int $telegram_id,
        ?string $description = null,
        ?User $user = null,
        ?Client $client = null,
    ){
        $this->id = $id;
        $this->user_id = $user_id;
        $this->client_id = $client_id;
        $this->telegram_id = $telegram_id;
        $this->description = $description;
        $this->user = $user;
        $this->client = $client;
    }

    public function toArray() : array {
        return [
            "id" => $this->id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "telegram_id" => $this->telegram_id,
            "description" => $this->description,
            "user" => $this->user,
            "client" => $this->client,
        ];
    }
    public function toStoreArray() : array {
        return [
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "telegram_id" => $this->telegram_id,
            "description" => $this->description,
        ];
    }
    public function toUpdateArray() : array {
        return [
            "client_id" => $this->client_id,
            "description" => $this->description,
        ];
    }
}

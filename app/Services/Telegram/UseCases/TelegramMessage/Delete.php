<?php

namespace App\Services\Telegram\UseCases\TelegramMessage;

use App\Services\Telegram\Repositories\TelegramMessageRepository;
use Exception;

class Delete
{
    private TelegramMessageRepository $telegramMessageRepository;

    public function __construct(TelegramMessageRepository $telegramMessageRepository) {
        $this->telegramMessageRepository = $telegramMessageRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $telegramMessage = $this->telegramMessageRepository->fetchById($id);

        if($telegramMessage->organization_id !== $organization_id){
            throw new Exception(
                "This telegramMessage don't belong to this organization." ,
                403
            );
        }

        return $this->telegramMessageRepository->delete($telegramMessage);
    }
}

<?php

namespace App\Services\Telegram\UseCases\TelegramMessage;

use App\Services\Telegram\Domains\TelegramMessage;
use App\Services\Telegram\Factories\TelegramMessageFactory;
use App\Http\Requests\TelegramMessage\UpdateRequest;
use App\Services\Telegram\Repositories\TelegramMessageRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private TelegramMessageRepository $telegramMessageRepository;
    private TelegramMessageFactory $telegramMessageFactory;

    public function __construct(TelegramMessageRepository $telegramMessageRepository, TelegramMessageFactory $telegramMessageFactory) {
        $this->telegramMessageRepository = $telegramMessageRepository;
        $this->telegramMessageFactory = $telegramMessageFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return TelegramMessage
     */
    public function perform(UpdateRequest $request, int $id) : TelegramMessage {
        DB::beginTransaction();

        $domain = $this->telegramMessageFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $telegramMessage = $this->telegramMessageRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $telegramMessage;
    }
}

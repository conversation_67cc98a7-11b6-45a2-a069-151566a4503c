<?php

namespace App\Services\Telegram\UseCases;

use App\Services\Telegram\Domains\Messages\Test\WhoAmI;
use App\Services\Telegram\Telegram;
use App\Services\Telegram\Traits\Commands\CommercialCommands;
use App\Services\Telegram\Traits\Commands\InitialCommands;
use App\Services\Telegram\Traits\Commands\InventoryCommands;
use App\Services\Telegram\Traits\Commands\ReadDocCommands;
use Illuminate\Contracts\Container\BindingResolutionException;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Throwable;

class RunCommand
{
    use InitialCommands, InventoryCommands, CommercialCommands, ReadDocCommands;

    public Telegram $service;

    public function __construct(Telegram $service) {
        $this->service = $service;
    }

    public string $command;

    /**
     * @throws TelegramSDKException
     * @throws Throwable
     * @throws BindingResolutionException
     */
    public function run() : bool {
        $this->service->log("[RunCommand::perform message::text:]", [$this->service->message->text]);

        $this->command = $this->service->message->text ?? "";
        if ($this->isCallbackQuery()) {
            $this->runCallBackData();
        }
        $this->service->log("[RunCommand::perform command:]", [$this->command]);
        if ($this->isCommand()){
            $this->runCommand();
            return true;
        }

        $this->service->log("[RunCommand::perform]", ["NO COMMAND"]);
        return false;
    }

    private function runCallBackData() : void {
        $callbackData = $this->service->callbackData;
        switch ($callbackData) {
            default:
                $this->command = "/" . $callbackData;
                break;
        }
    }

    private function isCallbackQuery() : bool {
        if($this->service->callbackQuery && $this->service->callbackData) {
            return true;
        }
        return false;
    }

    private function isCommand() : bool {
        $normal_command = in_array($this->command, self::TELEGRAM_COMMANDS);
        $inventory_command = in_array($this->command, self::TELEGRAM_INVENTORY_COMMANDS);
        $commercial_command = in_array($this->command, self::TELEGRAM_COMMERCIAL_COMMANDS);
        $doc_read_command = in_array($this->command, self::TELEGRAM_DOC_READ_COMMANDS);

        return (
            $normal_command ||
            $inventory_command ||
            $commercial_command||
            $doc_read_command
        );
    }

    /**
     * @throws TelegramSDKException
     * @throws BindingResolutionException
     * @throws Throwable
     */
    private function runCommand() : void {
        $this->service->log("[Telegram::runCommand]", [$this->command]);

        $this->runInitialCommands();
        $this->runInventoryCommands();
        $this->runCommercialCommands();
        $this->runReadDocCommands();
        $this->runHiddenCommands();
    }

    private function runHiddenCommands() : void {
        switch ($this->command) {
            case "/quem-sou-eu":
                new WhoAmI($this->service);
                break;
        }
    }


}

<?php

namespace App\Services\Telegram\UseCases\TelegramChat;

use App\Services\Telegram\Repositories\TelegramChatRepository;
use Exception;

class Delete
{
    private TelegramChatRepository $telegramChatRepository;

    public function __construct(TelegramChatRepository $telegramChatRepository) {
        $this->telegramChatRepository = $telegramChatRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $telegramChat = $this->telegramChatRepository->fetchById($id);

        if($telegramChat->organization_id !== $organization_id){
            throw new Exception(
                "This telegramChat don't belong to this organization." ,
                403
            );
        }

        return $this->telegramChatRepository->delete($telegramChat);
    }
}

<?php

namespace App\Services\Telegram\UseCases;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Factories\TelegramChatFactory;
use App\Services\Telegram\Repositories\TelegramChatRepository;
use App\Services\Telegram\Telegram;

class GetChat
{
    public TelegramChatRepository $chatRepository;
    public TelegramChatFactory $chatFactory;

    public function __construct(TelegramChatRepository $chatRepository, TelegramChatFactory $chatFactory) {
        $this->chatRepository = $chatRepository;
        $this->chatFactory = $chatFactory;
    }

    /**
     * @param Telegram $telegram
     * @return TelegramChat
     */
    public function perform(Telegram $telegram) : TelegramChat {
        $telegram->log("[GetChat::perform]", [
            "chat_id" => $telegram->message->chat->id, "from_id" => $telegram->message->from->id, "bot_id" => $telegram->telegram_bot_id
        ] ?? []);

        $chat = $this->chatRepository->fetchByChatId($telegram->message->chat->id);
        $telegram->log("[GetChat::fetchByChatId]", [$chat]);
        if(!$chat){
            return $this->chatRepository->store(
                $this->chatFactory->buildFromUpdate($telegram)
            );
        }
        return $chat;
    }
}

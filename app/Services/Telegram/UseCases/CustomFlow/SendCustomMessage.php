<?php

namespace App\Services\Telegram\UseCases\CustomFlow;

use App\Domains\ChatBot\Step;
use App\Services\Telegram\Telegram;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Telegram\Bot\Keyboard\Keyboard;

class SendCustomMessage
{
    public Telegram $telegram;
    public function __construct(Telegram $telegram) {
        $this->telegram = $telegram;
    }

    /**
     * @throws TelegramSDKException
     */
    public function perform(Step $message): void {
        $keyboard = Keyboard::make()->inline();
        foreach ($message->component->buttons ?? [] as $button) {
            $keyboard->row(
                [Keyboard::inlineButton(
                    ['text' => $button->text, 'callback_data' => $button->callback_data]
                )]
            );
        }
        $this->telegram->sendMessage(
            $message->component->text ?? "no text",
            $message->hasButtons() ? $keyboard : null,
        );
    }
}

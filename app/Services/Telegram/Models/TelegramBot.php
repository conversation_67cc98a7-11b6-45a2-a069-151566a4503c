<?php

namespace App\Services\Telegram\Models;

use App\Enums\TelegramBotStatus;
use App\Enums\TelegramBotPublishingStatus;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TelegramBot extends Model
{
    use SoftDeletes;

    protected $table = 'telegram_bots';

    protected $fillable = [
        'organization_id',
        'telegram_id',
        'bot',
        'token',
        'url',
        'name',
        'description',
        'is_active',
        'is_published',
        'status',
        'publishing_status',
    ];

    protected $casts = [
        'status' => TelegramBotStatus::class,
        'publishing_status' => TelegramBotPublishingStatus::class,
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
}

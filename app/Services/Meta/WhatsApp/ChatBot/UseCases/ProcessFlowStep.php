<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppInteractionRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppInteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Domains\ChatBot\Step;
use Exception;

class ProcessFlowStep
{
    protected WhatsAppInteractionRepository $interactionRepository;
    protected WhatsAppInteractionFactory $interactionFactory;
    protected ConditionalNavigationService $conditionalNavigationService;
    protected DynamicInputService $dynamicInputService;

    public function __construct(
        WhatsAppInteractionRepository $interactionRepository,
        WhatsAppInteractionFactory $interactionFactory,
        ConditionalNavigationService $conditionalNavigationService,
        DynamicInputService $dynamicInputService
    ) {
        $this->interactionRepository = $interactionRepository;
        $this->interactionFactory = $interactionFactory;
        $this->conditionalNavigationService = $conditionalNavigationService;
        $this->dynamicInputService = $dynamicInputService;
    }

    /**
     * Process current flow step and determine next action
     *
     * @param WhatsAppConversation $conversation
     * @param array $messageData
     * @return array
     * @throws Exception
     */
    public function perform(WhatsAppConversation $conversation, array $messageData): array
    {
        // Create interaction record for this message
        $interaction = $this->createInteraction($conversation, $messageData);

        // Get current step
        $currentStep = $conversation->current_step;

        if (!$currentStep) {
            throw new Exception('No current step found for conversation');
        }

        // Process step based on its type
        $stepResult = $this->processStepByType($currentStep, $interaction, $conversation);

        // Apply conditional navigation if applicable
        $stepResult = $this->applyConditionalNavigation($stepResult, $currentStep, $interaction);

        // Save interaction with result
        $interaction->result = json_encode($stepResult);
        $this->interactionRepository->save($interaction);

        return $stepResult;
    }

    /**
     * Create interaction record for the message
     */
    protected function createInteraction(WhatsAppConversation $conversation, array $messageData): WhatsAppInteraction
    {
        $interaction = $this->interactionFactory->buildFromWebhookData($messageData, $conversation);

        return $this->interactionRepository->save($interaction);
    }

    /**
     * Process step based on its type
     */
    protected function processStepByType(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        switch (true) {
            case $step->is_message:
                return $this->processMessageStep($step, $interaction, $conversation);

            case $step->is_interactive:
                return $this->processInteractiveStep($step, $interaction, $conversation);

            case $step->is_input:
                return $this->processInputStep($step, $interaction, $conversation);

            case $step->is_command:
                return $this->processCommandStep($step, $interaction, $conversation);

            default:
                return $this->processDefaultStep($step, $interaction, $conversation);
        }
    }

    /**
     * Process message step (just display message and move to next)
     */
    protected function processMessageStep(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        return [
            'type' => 'message',
            'step_id' => $step->id,
            'action' => 'send_message',
            'message' => $this->getStepMessage($step),
            'next_step' => $step->next_step,
            'move_to_next' => true,
        ];
    }

    /**
     * Process interactive step (buttons, lists)
     */
    protected function processInteractiveStep(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Check if user provided a valid interaction
        $selectedOption = $this->getSelectedOption($interaction);

        if ($selectedOption) {
            // User made a selection, process it
            return [
                'type' => 'interactive',
                'step_id' => $step->id,
                'action' => 'process_selection',
                'selection' => $selectedOption,
                'next_step' => $this->getNextStepForSelection($step, $selectedOption),
                'move_to_next' => true,
            ];
        } else {
            // Show interactive options
            return [
                'type' => 'interactive',
                'step_id' => $step->id,
                'action' => 'show_options',
                'message' => $this->getStepMessage($step),
                'options' => $this->getStepOptions($step),
                'move_to_next' => false,
            ];
        }
    }

    /**
     * Process input step (collect user input)
     */
    protected function processInputStep(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        $userInput = $interaction->getTextContent();

        if ($userInput) {
            // Process dynamic input (update domain objects)
            $inputResult = $this->dynamicInputService->processInputStep($step, $interaction, $conversation);

            if ($inputResult['success']) {
                return [
                    'type' => 'input',
                    'step_id' => $step->id,
                    'action' => 'input_processed',
                    'input' => $userInput,
                    'updated_field' => $inputResult['updated_field'],
                    'updated_value' => $inputResult['updated_value'],
                    'message' => "✅ Got it! Your {$inputResult['updated_field']} has been saved.",
                    'next_step' => $step->next_step,
                    'move_to_next' => true,
                ];
            } else {
                // Handle input processing failure
                if ($inputResult['finish_flow'] ?? false) {
                    return [
                        'type' => 'input',
                        'step_id' => $step->id,
                        'action' => 'input_error_finish',
                        'error' => $inputResult['error'],
                        'message' => "❌ {$inputResult['error']}",
                        'move_to_next' => false,
                        'finish_conversation' => true,
                    ];
                } else {
                    return [
                        'type' => 'input',
                        'step_id' => $step->id,
                        'action' => 'input_invalid',
                        'error' => $inputResult['error'],
                        'message' => "❌ {$inputResult['error']} Please try again.",
                        'move_to_next' => false,
                    ];
                }
            }
        } else {
            // Request input
            return [
                'type' => 'input',
                'step_id' => $step->id,
                'action' => 'request_input',
                'message' => $this->getStepMessage($step),
                'move_to_next' => false,
            ];
        }
    }

    /**
     * Process command step (execute business logic)
     */
    protected function processCommandStep(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // TODO: Implement command processing logic
        // This would execute specific business logic based on step configuration

        return [
            'type' => 'command',
            'step_id' => $step->id,
            'action' => 'command_executed',
            'result' => 'Command processing not yet implemented',
            'next_step' => $step->next_step,
            'move_to_next' => true,
        ];
    }

    /**
     * Process default step
     */
    protected function processDefaultStep(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        return [
            'type' => 'default',
            'step_id' => $step->id,
            'action' => 'default_processing',
            'message' => $this->getStepMessage($step),
            'next_step' => $step->next_step,
            'move_to_next' => true,
        ];
    }

    /**
     * Get message content for step
     */
    protected function getStepMessage(Step $step): string
    {
        // First try to get message from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData) && isset($jsonData['message'])) {
                return $jsonData['message'];
            }
        }

        // Fallback to step name
        return $step->step ?? 'Step message not configured';
    }

    /**
     * Get options for interactive step
     */
    protected function getStepOptions(Step $step): array
    {
        // Try to get options from step components/buttons
        if ($step->components && !empty($step->components)) {
            $options = [];
            foreach ($step->components as $component) {
                if ($component->buttons && !empty($component->buttons)) {
                    foreach ($component->buttons as $button) {
                        $options[] = $button->text ?? 'Button';
                    }
                }
            }
            return $options;
        }

        // Try to get options from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData) && isset($jsonData['options'])) {
                return $jsonData['options'];
            }
        }

        return [];
    }

    /**
     * Get selected option from interaction
     */
    protected function getSelectedOption(WhatsAppInteraction $interaction): ?string
    {
        if ($interaction->isButtonInteraction()) {
            return $interaction->getButtonId();
        }

        if ($interaction->isListInteraction()) {
            return $interaction->getListSelectionId();
        }

        return null;
    }

    /**
     * Get next step based on user selection
     */
    protected function getNextStepForSelection(Step $step, string $selection): ?int
    {
        // TODO: Implement logic to determine next step based on selection
        // This would parse step configuration to find conditional next steps
        return $step->next_step;
    }

    /**
     * Validate user input for input steps
     */
    protected function validateInput(Step $step, string $input): array
    {
        // TODO: Implement input validation based on step configuration
        // For now, accept all input as valid
        return ['valid' => true, 'error' => null];
    }

    /**
     * Apply conditional navigation to step result
     */
    protected function applyConditionalNavigation(
        array $stepResult,
        Step $currentStep,
        WhatsAppInteraction $interaction
    ): array {
        // Only apply conditional navigation if we're moving to next step
        if (!($stepResult['move_to_next'] ?? false)) {
            return $stepResult;
        }

        // Check if current step has conditional navigation
        if (!$this->conditionalNavigationService->hasConditionalNavigation($currentStep)) {
            return $stepResult;
        }

        // Get the next step based on user interaction
        $conditionalNextStep = $this->conditionalNavigationService->getNextStepForInteraction($currentStep, $interaction);

        if ($conditionalNextStep) {
            // Override the next step with conditional target
            $stepResult['next_step'] = $conditionalNextStep->id;
            $stepResult['conditional_navigation'] = true;
            $stepResult['target_step_identifier'] = $conditionalNextStep->step;

            \Log::info('Conditional navigation applied', [
                'from_step' => $currentStep->step,
                'to_step' => $conditionalNextStep->step,
                'button_id' => $interaction->getButtonId(),
                'interaction_type' => $interaction->whatsapp_message_type
            ]);
        }

        return $stepResult;
    }
}

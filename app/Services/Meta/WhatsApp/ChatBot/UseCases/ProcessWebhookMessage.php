<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use Exception;

class ProcessWebhookMessage
{
    /**
     * Process and validate webhook message data
     *
     * @param array $webhookData
     * @return array
     * @throws Exception
     */
    public function perform(array $webhookData): array
    {
        $message = $webhookData['message'] ?? [];
        $metadata = $webhookData['metadata'] ?? [];
        $contacts = $webhookData['contacts'] ?? [];

        // Validate required fields
        if (empty($message['from'])) {
            throw new Exception('Missing sender phone number in webhook data');
        }

        if (empty($message['id'])) {
            throw new Exception('Missing message ID in webhook data');
        }

        // Extract phone number (remove country code prefix if present)
        $fromNumber = $this->normalizePhoneNumber($message['from']);
        
        // Extract contact information
        $contactInfo = $this->extractContactInfo($contacts, $message['from']);
        
        // Extract message content based on type
        $messageType = $message['type'] ?? 'text';
        $messageContent = $this->extractMessageContent($message);

        // Build standardized message data
        return [
            'message_id' => $message['id'],
            'from' => $fromNumber,
            'original_from' => $message['from'],
            'type' => $messageType,
            'content' => $messageContent,
            'timestamp' => $message['timestamp'] ?? time(),
            'contact_name' => $contactInfo['name'] ?? null,
            'profile_name' => $contactInfo['profile_name'] ?? null,
            'phone_number_id' => $metadata['phone_number_id'] ?? null,
            'display_phone_number' => $metadata['display_phone_number'] ?? null,
            'raw_message' => $message,
            'raw_metadata' => $metadata,
            'raw_contacts' => $contacts,
        ];
    }

    /**
     * Normalize phone number format
     */
    protected function normalizePhoneNumber(string $phoneNumber): string
    {
        // Remove any non-digit characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Remove leading country codes (common ones)
        $patterns = [
            '/^55/',  // Brazil
            '/^1/',   // US/Canada
            '/^44/',  // UK
            '/^49/',  // Germany
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $cleaned)) {
                $cleaned = preg_replace($pattern, '', $cleaned, 1);
                break;
            }
        }
        
        return $cleaned;
    }

    /**
     * Extract contact information from contacts array
     */
    protected function extractContactInfo(array $contacts, string $phoneNumber): array
    {
        foreach ($contacts as $contact) {
            if (($contact['wa_id'] ?? null) === $phoneNumber) {
                return [
                    'name' => $contact['profile']['name'] ?? null,
                    'profile_name' => $contact['profile']['name'] ?? null,
                ];
            }
        }
        
        return [];
    }

    /**
     * Extract message content based on message type
     */
    protected function extractMessageContent(array $message): ?string
    {
        $type = $message['type'] ?? 'text';

        switch ($type) {
            case 'text':
                return $message['text']['body'] ?? null;
                
            case 'interactive':
                return $this->extractInteractiveContent($message['interactive'] ?? []);
                
            case 'button':
                return $message['button']['text'] ?? null;
                
            case 'image':
            case 'document':
            case 'audio':
            case 'video':
                return "Media message: {$type}";
                
            case 'location':
                $location = $message['location'] ?? [];
                return "Location: {$location['latitude']}, {$location['longitude']}";
                
            default:
                return "Unsupported message type: {$type}";
        }
    }

    /**
     * Extract content from interactive messages (buttons, lists)
     */
    protected function extractInteractiveContent(array $interactive): ?string
    {
        $type = $interactive['type'] ?? null;

        switch ($type) {
            case 'button_reply':
                return $interactive['button_reply']['title'] ?? null;
                
            case 'list_reply':
                return $interactive['list_reply']['title'] ?? null;
                
            default:
                return "Interactive message: {$type}";
        }
    }
}

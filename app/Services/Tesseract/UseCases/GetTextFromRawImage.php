<?php

namespace App\Services\Tesseract\UseCases;

use App\Helpers\File;
use App\Http\Requests\RawImage\ReadRequest;
use App\Services\Tesseract\Domains\RawImage;
use App\Services\Tesseract\Factories\RawImageFactory;

class GetTextFromRawImage
{
    private const FILE_STRING = "file";

    public RawImageFactory $rawImageFactory;

    public function __construct(RawImageFactory $rawImageFactory)
    {
        $this->rawImageFactory = $rawImageFactory;
    }

    public function perform(ReadRequest $request) : RawImage {
        $file = new File($request->file(self::FILE_STRING));

        $rawImage = $this->rawImageFactory->buildFromFile($file, $request);
        $rawImage->organization_id = request()->user()->organization_id;
        $rawImage->user_id = request()->user()->id;

        $rawImage->process();

        return $rawImage;
    }
}

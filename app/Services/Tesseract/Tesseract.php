<?php

namespace App\Services\Tesseract;

use App\Helpers\DBLog;
use App\Helpers\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use thiagoalessio\TesseractOCR\TesseractOCR;

class Tesseract {

    private TesseractOCR $ocr;

    public ?string $lang = null;
    public ?string $text = null;

    public ?File $file;

    public function __construct(
        string $lang = "por",
        ?File $file = null
    ) {
        $this->ocr = new TesseractOCR();

        $this->file = $file;
        $this->lang = $lang;
    }

    public function setLang(string|array|null $lang = null) : void {
        if ($lang) {
            $this->lang = $lang;
        }
        $this->ocr->lang($this->lang);
    }

    public function load(string $image) : void {
        Log::info("[OCR::Tesseract::public_path", [Storage::disk("public")->path($image)]);
        $this->ocr->image(
            Storage::disk("public")->path($image)
        );

        $this->text = $this->ocr->run();
    }

    public function loadRaw(string $path) : void {
        try {
            Log::info("[OCR::Tesseract::loadRaw::path", [$path]);
            $this->ocr->image($path);
            $this->text = $this->ocr->run();
        } catch (\Throwable $e){
            DBLog::logError(
                $e->getMessage(),
                "[OCR::Tesseract::loadRaw::path]",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null
            );
        }
    }

}

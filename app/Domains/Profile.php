<?php

namespace App\Domains;

use Carbon\Carbon;

class Profile
{
    public ?int $id;
    public string $name;
    public string $slug;
    public ?string $description;
    public ?bool $is_admin;
    public ?bool $is_super_admin;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        string $name,
        string $slug,
        ?string $description,
        ?bool $is_admin,
        ?bool $is_super_admin,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
    ){
        $this->id = $id;
        $this->name = $name;
        $this->slug = $slug;
        $this->description = $description;
        $this->is_admin = $is_admin;
        $this->is_super_admin = $is_super_admin;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
            "is_admin" => $this->is_admin,
            "is_super_admin" => $this->is_super_admin,
            "created_at" => $this->created_at->format("Y-m-d H:i:s"),
            "updated_at" => $this->updated_at->format("Y-m-d H:i:s")
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
            "is_admin" => $this->is_admin,
            "is_super_admin" => $this->is_super_admin,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
            "is_admin" => $this->is_admin,
            "is_super_admin" => $this->is_super_admin,
        ];
    }
}

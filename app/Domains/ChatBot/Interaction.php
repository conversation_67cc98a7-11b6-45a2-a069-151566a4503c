<?php

namespace App\Domains\ChatBot;

use App\Domains\User;
use Carbon\Carbon;
use App\Domains\ChatBot\Conversation;

class Interaction
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $client_id;
    public ?int $flow_id;
    public ?int $step_id;
    public ?int $conversation_id;
    public ?string $message;
    public ?string $answer;
    public ?string $result;
    public ?string $json;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Flow $flow;
    public ?Step $step;
    public ?Conversation $conversation;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?int $user_id = null,
        ?int $client_id = null,
        ?int $flow_id = null,
        ?int $step_id = null,
        ?int $conversation_id = null,
        ?string $message = null,
        ?string $answer = null,
        ?string $result = null,
        ?string $json = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Flow $flow = null,
        ?Step $step = null,
        ?Conversation $conversation = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->client_id = $client_id;
        $this->flow_id = $flow_id;
        $this->step_id = $step_id;
        $this->conversation_id = $conversation_id;
        $this->message = $message;
        $this->answer = $answer;
        $this->result = $result;
        $this->json = $json;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->flow = $flow;
        $this->step = $step;
        $this->conversation = $conversation;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "step_id" => $this->step_id,
            "conversation_id" => $this->conversation_id,
            "message" => $this->message,
            "answer" => $this->answer,
            "result" => $this->result,
            "json" => $this->json,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => $this->user?->toArray(),
            "flow" => $this->flow?->toArray(),
            "step" => $this->step?->toArray(),
            "conversation" => $this->conversation?->toArray(),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "step_id" => $this->step_id,
            "conversation_id" => $this->conversation_id,
            "message" => $this->message,
            "answer" => $this->answer,
            "result" => $this->result,
            "json" => $this->json,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "step_id" => $this->step_id,
            "conversation_id" => $this->conversation_id,
            "message" => $this->message,
            "answer" => $this->answer,
            "result" => $this->result,
            "json" => $this->json,
        ];
    }
}

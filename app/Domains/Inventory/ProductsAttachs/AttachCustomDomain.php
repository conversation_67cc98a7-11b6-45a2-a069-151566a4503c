<?php

namespace App\Domains\Inventory\ProductsAttachs;

use App\Models\CustomProduct;

class AttachCustomDomain
{

    public array $products = [];

    public function __construct(array $products){
        foreach ($products as $product){
            $this->products[] = new CustomProduct([
                "quantity" => $product["quantity"],
                "value" => $product["value"],
                "description" => $product["description"],
            ]);
        }
    }
}

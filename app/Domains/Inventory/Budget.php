<?php

namespace App\Domains\Inventory;

use Carbon\Carbon;

class Budget
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $client_id;
    public ?float $value;
    public ?float $cost;
    public string $name;
    public ?string $description;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Client $client;

    /** @var BudgetProduct[] $products */
    public ?array $products;

    /** @var CustomProduct[] $customProducts */
    public ?array $customProducts;

    /** @var Project[] $projects */
    public ?array $projects;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $client_id,
        ?float $value,
        ?float $cost,
        string $name,
        ?string $description,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Client $client = null,
        ?array $products = null,
        ?array $customProducts = null,
        ?array $projects = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->client_id = $client_id;
        $this->value = $value;
        $this->cost = $cost;
        $this->name = $name;
        $this->description = $description;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->client = $client;
        $this->products = $products;
        $this->customProducts = $customProducts;
        $this->projects = $projects;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "client_id" => $this->client_id,
            "value" => $this->value,
            "cost" => $this->cost,
            "name" => $this->name,
            "description" => $this->description,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "client" => ($this->client) ? $this->client->toArray() : null,
            "products" => $this->products,
            "customProducts" => $this->customProducts,
            "projects" => $this->projects
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "client_id" => $this->client_id,
            "value" => $this->value,
            "cost" => $this->cost,
            "name" => $this->name,
            "description" => $this->description,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "client_id" => $this->client_id,
            "value" => $this->value,
            "cost" => $this->cost,
            "name" => $this->name,
            "description" => $this->description,
        ];
    }

    public function getProductsIds() : array {
        $product_ids = [];
        foreach ($this->products as $product){
            $product_ids[] = $product->product_id;
        }
        return $product_ids;
    }
    public function exportProducts() : array {
        $product_ids = [];
        foreach ($this->products as $product){
            $product_ids[$product->product_id] = [
                'value' => $product->value,
                'quantity' => $product->quantity,
                'description' => $product->description,
            ];
        }
        return $product_ids;
    }

}

<?php

namespace App\Domains\Inventory\Report;

class StockExitGroup
{
    public ?string $group;
    public ?int $total_quantity;
    public ?float $total_value;

    /** @var StockExitRow[] $stock_exits  */
    public ?array $stock_exits;

    public function __construct(
        ?string $group,
        ?int $total_quantity,
        ?float $total_value,
        ?array $stock_exits
    ){
        $this->group = $group;
        $this->total_quantity = $total_quantity;
        $this->total_value = $total_value;
        $this->stock_exits = $stock_exits;
    }

    public function toArray(): array
    {
        return [
            "group" => $this->group,
            "total_quantity" => $this->total_quantity,
            "total_value" => $this->total_value,
            "stock_exits" => $this->stock_exits,
        ];
    }
}

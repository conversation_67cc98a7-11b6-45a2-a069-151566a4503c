<?php

namespace App\Domains\Filters;

class InteractionFilters extends Filters
{
    public const ALLOWED_FILTERS = [
        'organization_id',
        'user_id',
        'client_id',
        'flow_id',
        'step_id',
        'conversation_id',
        'message',
        'answer',
        'result',
    ];

    public function __construct(array $requestData)
    {
        parent::__construct(self::ALLOWED_FILTERS, $requestData);
    }
}

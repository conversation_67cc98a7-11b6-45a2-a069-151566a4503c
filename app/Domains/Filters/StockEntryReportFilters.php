<?php

namespace App\Domains\Filters;

class StockEntryReportFilters extends Filters
{
    public const ALLOWED_FILTERS = [
        'created_at_greater_than',
        'created_at_lower_than',
        'user_id',
        'brand_id',
        'product_id',
        'client_id',
        'project_id',
    ];

    public function __construct(array $requestData)
    {
        parent::__construct(self::ALLOWED_FILTERS, $requestData);
    }
}

<?php

namespace App\Domains\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\Importable;
use App\Models\Budget;

class ImportBudget extends Core implements ToCollection
{
    use Importable;

    public bool $skipHeader = false;

    public array $columns_sql_to_fill = [
        'client_id',
        'value',
        'cost',
        'name',
        'description',
    ];

    public function collection(Collection $collection) : void {
        $rows = $this->skipHeader ? $collection->skip(1) : $collection;
        foreach ($rows as $row) {
            Budget::create([
                'organization_id' => $this->organization_id,
                'client_id' => $this->getClientIdByClient($row),
                'value' => $this->getRowIndexFromMap($row, 'value'),
                'cost' => $this->getRowIndexFromMap($row, 'cost'),
                'name' => $this->getRowIndexFromMap($row, 'name'),
                'description' => $this->getRowIndexFromMap($row, 'description'),
            ]);
        }
    }
}

<?php

namespace App\Domains\Imports;

use App\Helpers\DBLog;
use App\Models\Brand;
use App\Models\Budget;
use App\Models\Client;
use App\Models\Product;
use App\Models\User;
use App\Models\Project;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class Core {

    public int $organization_id;
    public array $map = [];
    public array $columns_sql_to_fill = [];

    public function getDateRowIndexFromMap(mixed $row, string $index) : mixed {
        $date = $this->getRowIndexFromMap($row, $index);
        if(!is_null($date)){
            try{
                $date = Carbon::parse($date);
            } catch (\Throwable $exception) {
                DBLog::logError(
                    "[ERROR::getDateRowIndexFromMap]: ". $exception->getMessage(),
                    "import",
                    $this->organization_id ?? null,
                    request()->user()->id ?? null,
                    [$row, $index] ?? null
                );
                try{
                    $date = Carbon::createFromFormat('d/m/Y H:i:s', $date);
                }catch(\Throwable $e) {
                    DBLog::logError(
                        "[ERROR::getDateRowIndexFromMap]: ". $e->getMessage(),
                        "import",
                        $this->organization_id ?? null,
                        request()->user()->id ?? null,
                        [$row, $index] ?? null
                    );
                }
            }
        }
        return $date;
    }

    public function getRowIndexFromMap(mixed $row, string $index) : mixed {
        if(is_null($this->map[$index])){
            return null;
        }
        if(!empty($this->map[$index]) && $this->map[$index]){
            return $row[$this->map[$index]];
        }
        if(!is_null($this->map[$index]) && !$this->map[$index] && $this->map[$index] !== false){
            if( ((int) $this->map[$index]) === 0){
                return $row[0];
            }
        }
        return null;
    }

    public function toJson() : string {
        return json_encode($this->map);
    }
    public function getMap() : void {
        foreach ($this->columns_sql_to_fill as $column){
            $this->map[$column] = null;
        }
    }

    public function setMap(array $map) : void {
        $this->map = $map;
    }

    public function getBrandIdByBrand(mixed $row) : mixed {
        $brand = $this->getRowIndexFromMap($row, 'brand_id');
        if(is_numeric($brand)){
            return $brand;
        }

        return Brand::where('organization_id', $this->organization_id)
            ->where('name', $brand)
            ->first()->id ?? null;
    }

    public function shouldSendNull(mixed $value) : bool {
        try{
            return (
                is_null($value) ||
                strtolower($value) == "null" ||
                $value == "" ||
                $value == " "
            );
        } catch (\Throwable $e) {
            DBLog::logError(
                "[ERROR]: ". $e->getMessage(),
                "import",
                $this->organization_id ?? null,
                request()->user()->id ?? null,
                [$value] ?? null
            );
            return true;
        }
    }

    public function getOrCreateBrandIdByBrand(mixed $row) : mixed {
        $brandInRow = $this->getRowIndexFromMap($row, 'brand_id');
        $brand = $this->getBrandIdByBrand($row);
        if(is_null($brand)){
            if($this->shouldSendNull($brandInRow)){ return null; }
            $brand = $this->storeBrand($row);
        }
        return $brand;
    }

    private function storeBrand(mixed $row) : ?int {
        try{
            return Brand::create([
                'organization_id' => $this->organization_id,
                'name' => $this->getRowIndexFromMap($row, 'brand_id'),
                'description' => '',
            ])->id;
        } catch (\Throwable $exception){
            Log::error("[ERROR]: ". $exception->getMessage());
            DBLog::logError(
                "[ERROR]: ". $exception->getMessage(),
                "import",
                $this->organization_id ?? null,
                request()->user()->id ?? null,
                [$row] ?? null
            );
            return null;
        }
    }

    public function getProductIdByProduct(mixed $row) : mixed {
        $product = $this->getRowIndexFromMap($row, 'product_id');
        if(is_numeric($product)){
            return $product;
        }

        return Product::where('organization_id', $this->organization_id)
            ->where('name', $product)
            ->first()->id ?? null;
    }

    public function getClientIdByClient(mixed $row) : mixed {
        $client = $this->getRowIndexFromMap($row, 'client_id');
        if(is_numeric($client)){
            return $client;
        }

        return Client::where('organization_id', $this->organization_id)
            ->where('name', $client)
            ->first()->id ?? null;
    }

    public function getBudgetIdByBudget(mixed $row) : mixed {
        $budget = $this->getRowIndexFromMap($row, 'budget_id');
        if(is_numeric($budget)){
            return $budget;
        }

        return Budget::where('organization_id', $this->organization_id)
            ->where('name', $budget)
            ->first()->id ?? null;
    }

    public function getUserIdByUser(mixed $row) : mixed {
        $user = $this->getRowIndexFromMap($row, 'user_id');
        if(is_numeric($user)){
            return $user;
        }

        return User::where('organization_id', $this->organization_id)
            ->where('name', $user)
            ->first()->id ?? null;
    }

    public function getProjectIdByProject(mixed $row) : mixed {
        $project = $this->getRowIndexFromMap($row, 'project_id');
        if(is_numeric($project)){
            return $project;
        }

        return Project::where('organization_id', $this->organization_id)
            ->where('name', $project)
            ->first()->id ?? null;
    }
}

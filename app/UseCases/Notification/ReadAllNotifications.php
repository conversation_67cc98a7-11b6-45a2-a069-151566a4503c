<?php

namespace App\UseCases\Notification;

use Exception;

class ReadAllNotifications
{
    public function __construct() {
    }

    /**
     * @return Notification
     * @throws Exception
     */
    public function perform() {
        $notifications = auth()->user()->unreadNotifications;
        foreach ($notifications as $notification) {
            $notification->markAsRead();
        }
        return true;
    }
}

<?php

namespace App\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Repositories\ProjectProductRepository;
use Exception;

class Get
{
    private ProjectProductRepository $projectProductRepository;

    public function __construct(ProjectProductRepository $projectProductRepository) {
        $this->projectProductRepository = $projectProductRepository;
    }

    /**
     * @param int $id
     * @return ProjectProduct
     * @throws Exception
     */
    public function perform(int $id) : ProjectProduct {
        return $this->projectProductRepository->fetchById($id);
    }
}

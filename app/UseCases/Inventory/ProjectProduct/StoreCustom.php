<?php

namespace App\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Factories\Inventory\ProjectProductFactory;
use App\Http\Requests\ProjectProduct\StoreRequest;
use App\Repositories\ProjectProductRepository;

class StoreCustom
{
    private ProjectProductRepository $projectProductRepository;
    private ProjectProductFactory $projectProductFactory;

    public function __construct(ProjectProductRepository $projectProductRepository, ProjectProductFactory $projectProductFactory) {
        $this->projectProductRepository = $projectProductRepository;
        $this->projectProductFactory = $projectProductFactory;
    }

    /**
     * @param StoreRequest $request
     * @return ProjectProduct
     */
    public function perform(StoreRequest $request) : ProjectProduct {

        $domain = $this->projectProductFactory->buildFromStoreRequest($request);

        return $this->projectProductRepository->store($domain);
    }
}

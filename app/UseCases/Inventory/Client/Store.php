<?php

namespace App\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Client\StoreRequest;
use App\Repositories\ClientRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ClientRepository $clientRepository;
    private ClientFactory $clientFactory;

    public function __construct(ClientRepository $clientRepository, ClientFactory $clientFactory) {
        $this->clientRepository = $clientRepository;
        $this->clientFactory = $clientFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Client
     */
    public function perform(StoreRequest $request) : Client {
        DB::beginTransaction();

        $domain = $this->clientFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $client = $this->clientRepository->store($domain);

        DB::commit();

        return $client;
    }
}

<?php

namespace App\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\ProductHistory;
use App\Repositories\ProductHistoryRepository;
use Exception;

class Get
{
    private ProductHistoryRepository $productHistoryRepository;

    public function __construct(ProductHistoryRepository $productHistoryRepository) {
        $this->productHistoryRepository = $productHistoryRepository;
    }

    /**
     * @param int $id
     * @return ProductHistory
     * @throws Exception
     */
    public function perform(int $id) : ProductHistory {
        return $this->productHistoryRepository->fetchById($id);
    }
}

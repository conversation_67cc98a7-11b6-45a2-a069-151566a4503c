<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Domains\Inventory\StockExit;
use App\Factories\Inventory\StockFactory;
use App\Repositories\StockRepository;
use Exception;

class DecreaseStock
{
    private StockRepository $stockRepository;
    private StockFactory $stockFactory;

    public function __construct(
        StockRepository $stockRepository,
        StockFactory $stockFactory
    ) {
        $this->stockRepository = $stockRepository;
        $this->stockFactory = $stockFactory;
    }

    /**
     * @param StockExit $stockExit
     * @return Stock
     *
     * @throws Exception
     */
    public function perform(StockExit $stockExit) : Stock {

        $stock = $this->stockRepository->fetchByProductId(
            $stockExit->product_id
        );

        if(!$stock){
            throw new Exception("There is not enough stock available of this product!");
        }

        $stock->decreaseStock($stockExit->quantity);

        return $this->stockRepository->update($stock, $stock->organization_id);
    }
}

<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Repositories\StockRepository;
use Exception;

class Get
{
    private StockRepository $stockRepository;

    public function __construct(StockRepository $stockRepository) {
        $this->stockRepository = $stockRepository;
    }

    /**
     * @param int $id
     * @return Stock
     * @throws Exception
     */
    public function perform(int $id) : Stock {
        $organization_id = request()->user()->organization_id;

        $stock = $this->stockRepository->fetchById($id);

        if($stock->organization_id !== $organization_id){
            throw new Exception(
                "This stock don't belong to this organization." ,
                403
            );
        }
        return $stock;
    }
}

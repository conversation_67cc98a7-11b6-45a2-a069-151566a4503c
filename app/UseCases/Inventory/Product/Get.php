<?php

namespace App\UseCases\Inventory\Product;

use App\Domains\Inventory\Product;
use App\Repositories\ProductRepository;
use Exception;

class Get
{
    private ProductRepository $productRepository;

    public function __construct(ProductRepository $productRepository) {
        $this->productRepository = $productRepository;
    }

    /**
     * @param int $id
     * @return Product
     * @throws Exception
     */
    public function perform(int $id) : Product {
        $organization_id = request()->user()->organization_id;

        $product = $this->productRepository->fetchById($id);

        if($product->organization_id !== $organization_id){
            throw new Exception(
                "This product don't belong to this organization." ,
                403
            );
        }
        return $product;
    }
}

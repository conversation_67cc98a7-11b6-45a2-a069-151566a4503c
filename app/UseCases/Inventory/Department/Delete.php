<?php

namespace App\UseCases\Inventory\Department;

use App\Repositories\DepartmentRepository;
use Exception;

class Delete
{
    private DepartmentRepository $departmentRepository;

    public function __construct(DepartmentRepository $departmentRepository) {
        $this->departmentRepository = $departmentRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $department = $this->departmentRepository->fetchById($id);

        if($department->organization_id !== $organization_id){
            throw new Exception(
                "This department don't belong to this organization." ,
                403
            );
        }

        return $this->departmentRepository->delete($department);
    }
}

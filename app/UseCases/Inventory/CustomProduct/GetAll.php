<?php

namespace App\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Repositories\CustomProductRepository;

class GetAll
{
    private CustomProductRepository $customProductRepository;

    public function __construct(CustomProductRepository $customProductRepository) {
        $this->customProductRepository = $customProductRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->customProductRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}

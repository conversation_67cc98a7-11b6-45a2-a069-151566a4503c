<?php

namespace App\UseCases\Inventory\Shop;

use App\Domains\Inventory\Shop;
use App\Factories\Inventory\ShopFactory;
use App\Http\Requests\Shop\UpdateRequest;
use App\Repositories\ShopRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ShopRepository $shopRepository;
    private ShopFactory $shopFactory;

    public function __construct(ShopRepository $shopRepository, ShopFactory $shopFactory) {
        $this->shopRepository = $shopRepository;
        $this->shopFactory = $shopFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Shop
     */
    public function perform(UpdateRequest $request, int $id) : Shop {
        DB::beginTransaction();
        $domain = $this->shopFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $shop = $this->shopRepository->update(
            $domain,
            request()->user()->organization_id
        );
        DB::commit();

        return $shop;
    }
}

<?php

namespace App\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\Batch;
use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockEntryFactory;
use App\Repositories\StockEntryRepository;
use App\UseCases\Inventory\Stock\IncreaseStock;
use Illuminate\Support\Facades\DB;

class CreateFromBatch
{
    private StockEntryRepository $stockEntryRepository;
    private StockEntryFactory $stockEntryFactory;

    public function __construct(StockEntryRepository $stockEntryRepository, StockEntryFactory $stockEntryFactory) {
        $this->stockEntryRepository = $stockEntryRepository;
        $this->stockEntryFactory = $stockEntryFactory;
    }

    /**
     * @param Batch $batch
     * @return StockEntry
     */
    public function perform(Batch $batch) : StockEntry {
        DB::beginTransaction();

        $domain = $this->stockEntryFactory->buildFromBatch($batch, request()->user()->id);
        $domain->calculateValue();

        $stockEntry = $this->stockEntryRepository->store($domain);

        /** @var IncreaseStock $increaseStockUseCase */
        $increaseStockUseCase = app()->make(IncreaseStock::class);
        $increaseStockUseCase->perform($stockEntry);

        DB::commit();

        return $stockEntry;
    }
}

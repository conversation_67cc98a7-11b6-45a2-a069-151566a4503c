<?php

namespace App\UseCases\Inventory\BudgetProduct;

use App\Domains\Inventory\BudgetProduct;
use App\Repositories\BudgetProductRepository;

class GetAll
{
    private BudgetProductRepository $budgetProductRepository;

    public function __construct(BudgetProductRepository $budgetProductRepository) {
        $this->budgetProductRepository = $budgetProductRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->budgetProductRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}

<?php

namespace App\UseCases\Inventory\Project;

use App\Domains\Inventory\ProductsAttachs\AttachCustomDomain;
use App\Domains\Inventory\ProductsAttachs\AttachProductsDomain;
use App\Domains\Inventory\Project;
use App\Factories\Inventory\ProjectFactory;
use App\Repositories\ProjectRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AttachProducts
{
    private ProjectRepository $projectRepository;
    private ProjectFactory $projectFactory;

    public function __construct(ProjectRepository $projectRepository, ProjectFactory $projectFactory) {
        $this->projectRepository = $projectRepository;
        $this->projectFactory = $projectFactory;
    }

    /**
     * @param Request $request
     * @return Project
     */
    public function perform(Request $request, Project $project) : Project {
        DB::beginTransaction();

        $this->projectRepository->clearProducts($project);
        $this->projectRepository->clearCustoms($project);

        $products = new AttachProductsDomain($request->products ?? []);
        $customs = new AttachCustomDomain($request->customs ?? []);

        $this->projectRepository->attachProducts($project, $products);
        $this->projectRepository->attachCustoms($project, $customs);

        DB::commit();

        return $project;
    }
}

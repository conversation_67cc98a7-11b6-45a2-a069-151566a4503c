<?php

namespace App\UseCases\Inventory\GroupProduct;

use App\Repositories\GroupProductRepository;
use Exception;

class Delete
{
    private GroupProductRepository $groupProductRepository;

    public function __construct(GroupProductRepository $groupProductRepository) {
        $this->groupProductRepository = $groupProductRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        return $this->groupProductRepository->delete(
            $this->groupProductRepository->fetchById($id)
        );
    }
}

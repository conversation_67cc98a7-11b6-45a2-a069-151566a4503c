<?php

namespace App\UseCases\Inventory\Group;

use App\Domains\Inventory\Group;
use App\Factories\Inventory\GroupFactory;
use App\Http\Requests\Group\StoreRequest;
use App\Repositories\GroupRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private GroupRepository $groupRepository;
    private GroupFactory $groupFactory;

    public function __construct(GroupRepository $groupRepository, GroupFactory $groupFactory) {
        $this->groupRepository = $groupRepository;
        $this->groupFactory = $groupFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Group
     */
    public function perform(StoreRequest $request) : Group {
        DB::beginTransaction();

        $domain = $this->groupFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $group = $this->groupRepository->store($domain);

        DB::commit();

        return $group;
    }
}

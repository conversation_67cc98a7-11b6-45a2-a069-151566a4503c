<?php

namespace App\UseCases\Inventory\Group;

use App\Repositories\GroupRepository;
use Exception;

class Delete
{
    private GroupRepository $groupRepository;

    public function __construct(GroupRepository $groupRepository) {
        $this->groupRepository = $groupRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $group = $this->groupRepository->fetchById($id);

        if($group->organization_id !== $organization_id){
            throw new Exception(
                "This group don't belong to this organization." ,
                403
            );
        }

        return $this->groupRepository->delete($group);
    }
}

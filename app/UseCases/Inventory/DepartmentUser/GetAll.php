<?php

namespace App\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Repositories\DepartmentUserRepository;

class GetAll
{
    private DepartmentUserRepository $departmentUserRepository;

    public function __construct(DepartmentUserRepository $departmentUserRepository) {
        $this->departmentUserRepository = $departmentUserRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->departmentUserRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}

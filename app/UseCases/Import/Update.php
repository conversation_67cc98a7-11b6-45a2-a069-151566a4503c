<?php

namespace App\UseCases\Import;

use App\Domains\Imports\Import;
use App\Factories\ImportFactory;
use App\Http\Requests\Import\UpdateRequest;
use App\Repositories\ImportRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ImportRepository $importRepository;
    private ImportFactory $importFactory;

    public function __construct(ImportRepository $importRepository, ImportFactory $importFactory) {
        $this->importRepository = $importRepository;
        $this->importFactory = $importFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Import
     */
    public function perform(UpdateRequest $request, int $id) : Import {
        DB::beginTransaction();
        $domain = $this->importFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $import = $this->importRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $import;
    }
}

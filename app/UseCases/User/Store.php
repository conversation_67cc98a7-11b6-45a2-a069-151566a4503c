<?php

namespace App\UseCases\User;

use App\Domains\User;
use App\Factories\UserFactory;
use App\Http\Requests\User\StoreRequest;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private UserRepository $userRepository;
    private UserFactory $userFactory;

    public function __construct(UserRepository $userRepository, UserFactory $userFactory) {
        $this->userRepository = $userRepository;
        $this->userFactory = $userFactory;
    }

    /**
     * @param StoreRequest $request
     * @return User
     */
    public function perform(StoreRequest $request) : User {
        DB::beginTransaction();

        $domain = $this->userFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $user = $this->userRepository->store($domain);

        DB::commit();

        return $user;
    }
}

<?php

namespace App\UseCases\ChatBot\Step;

use App\Domains\ChatBot\Step;
use App\Factories\ChatBot\StepFactory;
use App\Http\Requests\Step\UpdateRequest;
use App\Repositories\StepRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private StepRepository $stepRepository;
    private StepFactory $stepFactory;

    public function __construct(StepRepository $stepRepository, StepFactory $stepFactory) {
        $this->stepRepository = $stepRepository;
        $this->stepFactory = $stepFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Step
     */
    public function perform(UpdateRequest $request, int $id) : Step {
        DB::beginTransaction();

        $domain = $this->stepFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $step = $this->stepRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $step;
    }
}

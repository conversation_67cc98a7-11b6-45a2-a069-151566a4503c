<?php

namespace App\UseCases\ChatBot\Template;

use App\Http\Requests\Template\StoreRequest;
use App\Domains\ChatBot\Template;
use App\Factories\ChatBot\TemplateFactory;
use App\Repositories\TemplateRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private TemplateRepository $templateRepository;
    private TemplateFactory $templateFactory;

    public function __construct(TemplateRepository $templateRepository, TemplateFactory $templateFactory) {
        $this->templateRepository = $templateRepository;
        $this->templateFactory = $templateFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Template
     */
    public function perform(StoreRequest $request) : Template {
        DB::beginTransaction();

        $domain = $this->templateFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $template = $this->templateRepository->store($domain);

        DB::commit();

        return $template;
    }
}

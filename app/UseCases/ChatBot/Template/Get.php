<?php

namespace App\UseCases\ChatBot\Template;

use App\Domains\ChatBot\Template;
use App\Repositories\TemplateRepository;
use Exception;

class Get
{
    private TemplateRepository $templateRepository;

    public function __construct(TemplateRepository $templateRepository) {
        $this->templateRepository = $templateRepository;
    }

    /**
     * @param int $id
     * @return Template
     * @throws Exception
     */
    public function perform(int $id) : Template {
        $organization_id = request()->user()->organization_id;

        $template = $this->templateRepository->fetchById($id);

        if($template->organization_id !== $organization_id){
            throw new Exception(
                "This template don't belong to this organization." ,
                403
            );
        }
        return $template;
    }
}

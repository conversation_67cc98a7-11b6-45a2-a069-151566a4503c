<?php

namespace App\UseCases\ChatBot\Interaction;

use App\Domains\ChatBot\Interaction;
use App\Factories\ChatBot\InteractionFactory;
use App\Http\Requests\Interaction\StoreRequest;
use App\Repositories\InteractionRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private InteractionRepository $interactionRepository;
    private InteractionFactory $interactionFactory;

    public function __construct(InteractionRepository $interactionRepository, InteractionFactory $interactionFactory) {
        $this->interactionRepository = $interactionRepository;
        $this->interactionFactory = $interactionFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Interaction
     */
    public function perform(StoreRequest $request) : Interaction {
        DB::beginTransaction();

        $domain = $this->interactionFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $interaction = $this->interactionRepository->store($domain);

        DB::commit();

        return $interaction;
    }
}

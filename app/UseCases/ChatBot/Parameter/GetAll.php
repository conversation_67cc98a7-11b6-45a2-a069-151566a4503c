<?php

namespace App\UseCases\ChatBot\Parameter;

use App\Domains\Filters\ParameterFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\ParameterRepository;

class GetAll
{
    private ParameterRepository $parameterRepository;

    public function __construct(ParameterRepository $parameterRepository) {
        $this->parameterRepository = $parameterRepository;
    }

    /**
     * @return array
     */
    public function perform(ParameterFilters $filters, OrderBy $orderBy) : array {
        return $this->parameterRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}

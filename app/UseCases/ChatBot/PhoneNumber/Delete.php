<?php

namespace App\UseCases\ChatBot\PhoneNumber;

use App\Repositories\PhoneNumberRepository;

class Delete
{
    private PhoneNumberRepository $phoneNumberRepository;

    public function __construct(PhoneNumberRepository $phoneNumberRepository) {
        $this->phoneNumberRepository = $phoneNumberRepository;
    }

    /**
     * @param int $id
     * @return bool
     */
    public function perform(int $id) : bool {
        return $this->phoneNumberRepository->delete($id);
    }
}
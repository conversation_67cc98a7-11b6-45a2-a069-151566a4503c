<?php

namespace App\UseCases\ChatBot\PhoneNumber;

use App\Domains\ChatBot\PhoneNumber;
use App\Repositories\PhoneNumberRepository;

class Get
{
    private PhoneNumberRepository $phoneNumberRepository;

    public function __construct(PhoneNumberRepository $phoneNumberRepository) {
        $this->phoneNumberRepository = $phoneNumberRepository;
    }

    /**
     * @param int $id
     * @return PhoneNumber
     */
    public function perform(int $id) : PhoneNumber {
        return $this->phoneNumberRepository->fetchById($id);
    }
}
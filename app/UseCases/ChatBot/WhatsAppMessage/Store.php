<?php

namespace App\UseCases\ChatBot\WhatsAppMessage;

use App\Http\Requests\WhatsAppMessage\StoreRequest;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private WhatsAppMessageRepository $whatsAppMessageRepository;
    private WhatsAppMessageFactory $whatsAppMessageFactory;

    public function __construct(
        WhatsAppMessageRepository $whatsAppMessageRepository,
        WhatsAppMessageFactory $whatsAppMessageFactory
    ) {
        $this->whatsAppMessageRepository = $whatsAppMessageRepository;
        $this->whatsAppMessageFactory = $whatsAppMessageFactory;
    }

    /**
     * Store a new WhatsApp message
     *
     * @param StoreRequest $request
     * @return WhatsAppMessage
     */
    public function perform(StoreRequest $request): WhatsAppMessage
    {
        DB::beginTransaction();

        try {
            $data = $request->validated();

            $whatsAppMessage = new WhatsAppMessage(
                null, // id
                $data['message_id'],
                null, // message - will be loaded if needed
                $data['whatsapp_message_id'] ?? null,
                $data['message_status'] ?? null,
                $data['wa_id'] ?? null,
                $data['input_phone'] ?? null,
                $data['messaging_product'] ?? 'whatsapp',
                $data['json'] ?? null,
                null, // created_at
                null  // updated_at
            );

            $savedWhatsAppMessage = $this->whatsAppMessageRepository->store($whatsAppMessage);

            DB::commit();

            return $savedWhatsAppMessage;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}

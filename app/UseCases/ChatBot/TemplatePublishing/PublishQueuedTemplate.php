<?php

namespace App\UseCases\ChatBot\TemplatePublishing;

use App\Domains\ChatBot\TemplatePublishing;
use App\Helpers\DBLog;
use App\Repositories\TemplatePublishingRepository;
use App\Repositories\TemplateRepository;
use App\Services\Meta\WhatsApp\TemplateService;
use Exception;

class PublishQueuedTemplate
{
    private TemplatePublishingRepository $templatePublishingRepository;
    private TemplateRepository $templateRepository;

    public function __construct(
        TemplatePublishingRepository $templatePublishingRepository,
        TemplateRepository $templateRepository,
    ) {
        $this->templatePublishingRepository = $templatePublishingRepository;
        $this->templateRepository = $templateRepository;
    }

    /**
     * Publishes a queued template
     *
     * @param TemplatePublishing $publishing
     * @return array Result with status and message
     */
    public function perform(TemplatePublishing $publishing): array
    {
        try {
            $template = $this->templateRepository->fetchById($publishing->template_id);
            if (!$template) {
                throw new Exception("Template not found: {$publishing->template_id}");
            }

            /** @var TemplateService $templateService */
            $templateService = app()->makeWith(TemplateService::class, [
                'phoneNumber' => $template->phone_number
            ]);

            $whatsAppTemplate = $templateService->register($template);

            $publishing->publish();

            $this->templatePublishingRepository->update($publishing, $publishing->template_id);

            DBLog::log(
                "WhatsApp template published successfully",
                "WhatsApp::PublishQueuedTemplate",
                $template->organization_id ?? null,
                $template->user_id ?? null,
                [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'whatsapp_template_id' => $whatsAppTemplate->external_id
                ]
            );

            return [
                'success' => true,
                'template' => $template,
                'message' => "Successfully published template: {$template->name} with status: {$whatsAppTemplate->status}"
            ];
        } catch (\Throwable $e) {
            $publishing->fail();
            $this->templatePublishingRepository->update($publishing, $publishing->template_id);

            DBLog::logError(
                "WhatsApp template publishing failed",
                "WhatsApp::PublishQueuedTemplate",
                $template->organization_id ?? null,
                $template->user_id ?? null,
                [
                    'template_id' => $publishing->template_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );

            return [
                'success' => false,
                'template_id' => $publishing->template_id,
                'message' => $e->getMessage()
            ];
        }
    }
}

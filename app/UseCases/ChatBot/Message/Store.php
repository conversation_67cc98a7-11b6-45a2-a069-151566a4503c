<?php

namespace App\UseCases\ChatBot\Message;

use App\Domains\ChatBot\Message;
use App\Factories\ChatBot\MessageFactory;
use App\Http\Requests\Message\StoreRequest;
use App\Repositories\MessageRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;

    public function __construct(MessageRepository $messageRepository, MessageFactory $messageFactory) {
        $this->messageRepository = $messageRepository;
        $this->messageFactory = $messageFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Message
     */
    public function perform(StoreRequest $request) : Message {
        DB::beginTransaction();

        $domain = $this->messageFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $message = $this->messageRepository->store($domain);

        DB::commit();

        return $message;
    }
}

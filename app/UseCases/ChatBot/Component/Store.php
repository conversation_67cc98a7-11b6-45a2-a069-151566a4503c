<?php

namespace App\UseCases\ChatBot\Component;

use App\Domains\ChatBot\Component;
use App\Factories\ChatBot\ComponentFactory;
use App\Http\Requests\Component\StoreRequest;
use App\Repositories\ComponentRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ComponentRepository $componentRepository;
    private ComponentFactory $componentFactory;

    public function __construct(ComponentRepository $componentRepository, ComponentFactory $componentFactory) {
        $this->componentRepository = $componentRepository;
        $this->componentFactory = $componentFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Component
     */
    public function perform(StoreRequest $request) : Component {
        DB::beginTransaction();

        $domain = $this->componentFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $component = $this->componentRepository->store($domain);

        DB::commit();

        return $component;
    }
}

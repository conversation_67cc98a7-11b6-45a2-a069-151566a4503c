<?php

namespace App\Factories\Report;

use App\Domains\Inventory\Report\StockExitReport;
use Illuminate\Support\Collection;

class StockExitReportFactory
{

    private StockExitGroupFactory $stockExitGroupFactory;

    public function __construct(
        StockExitGroupFactory $stockExitGroupFactory,
    ){
        $this->stockExitGroupFactory = $stockExitGroupFactory;
    }

    public function buildFromModels(Collection $exits, ?string $grouped_by) : StockExitReport {
        $total_quantity = 0;
        $total_value = 0.0;
        $groups = $this->stockExitGroupFactory->buildFromModels($exits, $grouped_by);
        foreach ($groups as $group){
            $total_quantity += $group->total_quantity;
            $total_value += $group->total_value;
        }
        return new StockExitReport(
            $total_quantity ?? 0,
            $total_value ?? 0.0,
            $groups ?? []
        );
    }
}

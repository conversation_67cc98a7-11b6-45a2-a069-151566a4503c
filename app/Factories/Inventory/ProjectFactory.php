<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Budget;
use App\Domains\Inventory\Project;
use App\Http\Requests\Project\StoreRequest;
use App\Http\Requests\Project\UpdateRequest;
use App\Models\Budget as BudgetModel;
use App\Models\Project as ProjectModel;
use Illuminate\Http\Request;

class ProjectFactory
{

    public ClientFactory $clientFactory;
    public ProductFactory $productFactory;
    public ProjectProductFactory $projectProductFactory;
    public BudgetFactory $budgetFactory;
    public CustomProductFactory $customProductFactory;

    public function __construct(
        ClientFactory $clientFactory,
        ProductFactory $productFactory,
        BudgetFactory $budgetFactory
    ) {
        $this->clientFactory = $clientFactory;
        $this->productFactory = $productFactory;
        $this->budgetFactory = $budgetFactory;
        $this->projectProductFactory = new ProjectProductFactory($this, $this->productFactory);
        $this->customProductFactory = new CustomProductFactory($budgetFactory, $this);
    }

    public function buildFromStoreRequest(StoreRequest $request) : Project {
        return new Project(
            null,
            $request->organization_id ?? null,
            $request->client_id ?? null,
            $request->budget_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->value ?? null,
            $request->cost ?? null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Project {
        return new Project(
            null,
            null,
            $request->client_id ?? null,
            $request->budget_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
            $request->value ?? null,
            $request->cost ?? null
        );
    }

    public function buildFromModel(
        ?ProjectModel $project,
        bool $with_client = true,
        bool $with_budget = true,
        bool $with_products = true,
        bool $with_custom_products = true,
    ) : ?Project {
        if(!$project){ return null; }

        $client = ($with_client) ? $this->clientFactory->buildFromModel($project->client) : null;
        $budget = ($with_budget) ? $this->budgetFactory->buildFromModel($project->budget, false, false, false) : null;
        $products = ($with_products) ? $this->projectProductFactory->buildFromProject($project) : null;
        $customProducts = ($with_custom_products) ? $this->customProductFactory->buildFromProject($project) : null;

        return new Project(
            $project->id ?? null,
            $project->organization_id ?? null,
            $project->client_id ?? null,
            $project->budget_id ?? null,
            $project->name ?? null,
            $project->description ?? null,
            $project->value ?? null,
            $project->cost ?? null,
            $project->created_at ?? null,
            $project->updated_at ?? null,
            $client ?? null,
            $budget ?? null,
            $products ?? null,
            $customProducts ?? null
        );
    }


     public function buildArrayFromBudget(BudgetModel $budget) : array {
            $projects = [];
            foreach ($budget->projects as $project){
                $projects[] = $this->buildFromModel($project, false, false, false);
            }
            return $projects;
     }
    public function buildFromBudget(Budget $budget, Request $request) : Project {
        return new Project(
            null,
            null,
            $budget->client_id,
            $budget->id,
            $request->name ?? "Project from budget #".$budget->id,
            $request->description ?? $budget->description,
            null,
            null,
            null,
            null,
            null,
        );
    }
}

<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Product;
use App\Http\Requests\Product\StoreRequest;
use App\Http\Requests\Product\UpdateRequest;
use App\Models\Product as ProductModel;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ProductFactory
{
    public BrandFactory $brandFactory;
    public function __construct(BrandFactory $brandFactory){
        $this->brandFactory = $brandFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Product {
        return new Product(
            null,
            $request->organization_id ?? null,
            $request->brand_id ?? null,
            $request->name ?? "",
            $request->barcode ?? "",
            $request->description ?? "",
            $request->price ?? 0.0,
            $request->unity ?? 0,
            Carbon::now() ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Product {
        return new Product(
            null,
            $request->organization_id ?? null,
            $request->brand_id ?? null,
            $request->name ?? "",
            $request->barcode ?? "",
            $request->description ?? "",
            $request->price ?? 0.0,
            $request->unity ?? 0,
            null,
        );
    }

    public function buildFromModel(?ProductModel $product) : ?Product {
        if(!$product) { return null; }

        return new Product(
            $product->id ?? null,
            $product->organization_id ?? null,
            $product->brand_id ?? null,
            $product->name ?? "",
            $product->barcode ?? "",
            $product->description ?? "",
            $product->price ?? null,
            is_numeric($product->unity) ? (int)$product->unity : null,
            $product->last_priced_at ? Carbon::parse($product->last_priced_at) : null,
            $product->created_at ?? null,
            $product->updated_at ?? null,
            $this->brandFactory->buildFromModel($product->brand ?? null) ?? null,
        );
    }

    public function buildFromModelArray(Collection $productModels) : array {
        $products = [];
        foreach ($productModels as $product){
            $products[] = $this->buildFromModel($product);
        }
        return $products;
    }
}

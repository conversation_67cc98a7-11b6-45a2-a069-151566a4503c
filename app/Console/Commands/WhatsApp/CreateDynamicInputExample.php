<?php

namespace App\Console\Commands\WhatsApp;

use Illuminate\Console\Command;
use App\Models\Flow;
use App\Models\Step;

class CreateDynamicInputExample extends Command
{
    protected $signature = 'whatsapp:create-input-example {organization_id=1}';
    protected $description = 'Create a flow example demonstrating dynamic input processing';

    public function handle()
    {
        $organizationId = $this->argument('organization_id');
        
        $this->info("Creating Dynamic Input Example Flow for Organization ID: {$organizationId}");

        // Create the flow
        $flow = Flow::create([
            'organization_id' => $organizationId,
            'name' => 'Client Registration Flow',
            'description' => 'Demonstrates dynamic input processing for client registration',
            'steps_count' => 6,
            'is_default_flow' => false,
            'json' => json_encode([
                'flow_type' => 'client_registration',
                'settings' => [
                    'allow_restart' => true,
                    'timeout_minutes' => 30
                ]
            ])
        ]);

        $this->info("✅ Flow created with ID: {$flow->id}");

        // Step 1: Welcome (identifier: "welcome")
        $step1 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'welcome',
            'type' => 'welcome',
            'position' => 1,
            'next_step' => null,
            'earlier_step' => null,
            'is_initial_step' => true,
            'is_ending_step' => false,
            'is_message' => true,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => false,
            'json' => json_encode([
                'message' => "Hello! 👋\n\nWelcome to our registration system. We'll help you create your profile in just a few steps.\n\nLet's get started!",
                'auto_advance' => true,
                'delay_seconds' => 2
            ])
        ]);

        // Step 2: Name Input (identifier: "name_input")
        $step2 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'name_input',
            'type' => 'name_input',
            'position' => 2,
            'next_step' => null,
            'earlier_step' => $step1->id,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => true, // ← This makes it an input step
            'json' => json_encode([
                'message' => "📝 **Step 1/4: Your Name**\n\nWhat's your full name?",
                'input_field' => 'client.name', // ← This tells us what to update
                'required' => true,
                'min_length' => 2,
                'max_length' => 100,
                'validation' => 'required|min:2|max:100'
            ])
        ]);

        // Step 3: Email Input (identifier: "email_input")
        $step3 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'email_input',
            'type' => 'email_input',
            'position' => 3,
            'next_step' => null,
            'earlier_step' => $step2->id,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => true,
            'json' => json_encode([
                'message' => "📧 **Step 2/4: Email Address**\n\nPlease enter your email address:",
                'input_field' => 'client.email',
                'required' => true,
                'validation' => 'required|email'
            ])
        ]);

        // Step 4: Phone Input (identifier: "phone_input")
        $step4 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'phone_input',
            'type' => 'phone_input',
            'position' => 4,
            'next_step' => null,
            'earlier_step' => $step3->id,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => true,
            'json' => json_encode([
                'message' => "📱 **Step 3/4: Phone Number**\n\nPlease enter your phone number (with area code):",
                'input_field' => 'client.phone',
                'required' => true,
                'min_length' => 10,
                'max_length' => 15,
                'validation' => 'required|min:10|max:15'
            ])
        ]);

        // Step 5: Address Input (identifier: "address_input")
        $step5 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'address_input',
            'type' => 'address_input',
            'position' => 5,
            'next_step' => null,
            'earlier_step' => $step4->id,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => true,
            'json' => json_encode([
                'message' => "🏠 **Step 4/4: Address**\n\nPlease enter your full address:",
                'input_field' => 'client.address',
                'required' => true,
                'min_length' => 10,
                'max_length' => 200,
                'validation' => 'required|min:10|max:200'
            ])
        ]);

        // Step 6: Completion (identifier: "completion")
        $step6 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'completion',
            'type' => 'completion',
            'position' => 6,
            'next_step' => null,
            'earlier_step' => $step5->id,
            'is_initial_step' => false,
            'is_ending_step' => true,
            'is_message' => true,
            'is_interactive' => false,
            'is_command' => true,
            'is_input' => false,
            'json' => json_encode([
                'message' => "🎉 **Registration Complete!**\n\nThank you {{client.name}}! Your profile has been created successfully.\n\n📋 **Your Information:**\n• Name: {{client.name}}\n• Email: {{client.email}}\n• Phone: {{client.phone}}\n• Address: {{client.address}}\n\nWelcome to our platform! 🚀",
                'command' => 'complete_registration'
            ])
        ]);

        // Update next_step references
        $step1->update(['next_step' => $step2->id]);
        $step2->update(['next_step' => $step3->id]);
        $step3->update(['next_step' => $step4->id]);
        $step4->update(['next_step' => $step5->id]);
        $step5->update(['next_step' => $step6->id]);

        $this->info("✅ Created 6 steps with dynamic input processing");

        // Update flow steps count
        $flow->update(['steps_count' => 6]);

        $this->info("🎉 Dynamic Input Example Flow created successfully!");
        $this->info("Flow ID: {$flow->id}");
        $this->info("");
        $this->info("📝 **Input Fields Configured:**");
        $this->info("   • name_input → client.name");
        $this->info("   • email_input → client.email");
        $this->info("   • phone_input → client.phone");
        $this->info("   • address_input → client.address");
        $this->info("");
        $this->info("✅ **Features Demonstrated:**");
        $this->info("   • Dynamic field updates based on user input");
        $this->info("   • Input validation (required, length, format)");
        $this->info("   • Automatic client profile building");
        $this->info("   • Variable substitution in completion message");
        $this->info("   • Error handling for invalid input");
        $this->info("");
        $this->info("🧪 **To test:**");
        $this->info("   1. Assign this flow to a phone number");
        $this->info("   2. Send a WhatsApp message to start registration");
        $this->info("   3. Follow the prompts to enter your information");
        $this->info("   4. Watch your client profile get updated in real-time");
        $this->info("   5. Try entering invalid data to see validation in action");
        
        return 0;
    }
}

<?php

namespace App\Console\Commands\DataSeed;

use App\Models\Button;
use App\Models\Component;
use App\Models\Flow;
use App\Models\Step;
use Illuminate\Console\Command;

class CreatePizzaFlowExample extends Command
{
    protected $signature = 'whatsapp:create-pizza-flow {organization_id=1}';
    protected $description = 'Create a sample pizza delivery flow for WhatsApp ChatBot';

    public function handle()
    {
        $organizationId = $this->argument('organization_id');

        $this->info("Creating Pizza Delivery Flow for Organization ID: {$organizationId}");

        // Create the flow
        $flow = Flow::create([
            'organization_id' => $organizationId,
            'name' => 'Pizza Delivery Flow',
            'description' => 'Complete pizza ordering flow for WhatsApp',
            'steps_count' => 5,
            'is_default_flow' => true,
            'json' => json_encode([
                'flow_type' => 'pizza_delivery',
                'settings' => [
                    'allow_restart' => true,
                    'timeout_minutes' => 30
                ]
            ])
        ]);

        $this->info("✅ Flow created with ID: {$flow->id}");

        // Step 1: Welcome Message
        $step1 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'Welcome to Pizza Express! 🍕',
            'type' => 'welcome',
            'position' => 1,
            'next_step' => null, // Will be set after creating step 2
            'earlier_step' => null,
            'is_initial_step' => true,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => true,
            'is_command' => false,
            'is_input' => false,
            'json' => json_encode([
                'message' => 'Welcome to Pizza Express! 🍕\n\nWhat would you like to do today?',
                'component_type' => 'interactive_buttons'
            ])
        ]);

        // Step 2: Menu Selection
        $step2 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'Choose your pizza size',
            'type' => 'menu_selection',
            'position' => 2,
            'next_step' => null, // Will be set after creating step 3
            'earlier_step' => $step1->id,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => true,
            'is_command' => false,
            'is_input' => false,
            'json' => json_encode([
                'message' => 'Great! Please choose your pizza size:',
                'component_type' => 'interactive_buttons'
            ])
        ]);

        // Step 3: Address Input
        $step3 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'Enter delivery address',
            'type' => 'address_input',
            'position' => 3,
            'next_step' => null, // Will be set after creating step 4
            'earlier_step' => $step2->id,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => true,
            'json' => json_encode([
                'message' => 'Please enter your delivery address:',
                'input_type' => 'text',
                'validation' => 'required|min:10'
            ])
        ]);

        // Step 4: Order Confirmation
        $step4 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'Confirm your order',
            'type' => 'confirmation',
            'position' => 4,
            'next_step' => null, // Will be set after creating step 5
            'earlier_step' => $step3->id,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => true,
            'is_command' => false,
            'is_input' => false,
            'json' => json_encode([
                'message' => 'Please confirm your order:',
                'component_type' => 'interactive_buttons'
            ])
        ]);

        // Step 5: Order Complete
        $step5 = Step::create([
            'organization_id' => $organizationId,
            'flow_id' => $flow->id,
            'step' => 'Order confirmed! Thank you!',
            'type' => 'completion',
            'position' => 5,
            'next_step' => null,
            'earlier_step' => $step4->id,
            'is_initial_step' => false,
            'is_ending_step' => true,
            'is_message' => true,
            'is_interactive' => false,
            'is_command' => true,
            'is_input' => false,
            'json' => json_encode([
                'message' => '🎉 Order confirmed!\n\nYour pizza will be delivered in 30-45 minutes.\n\nOrder ID: #{{order_id}}\nTotal: ${{total}}\n\nThank you for choosing Pizza Express!',
                'command' => 'create_order'
            ])
        ]);

        // Update next_step references
        $step1->update(['next_step' => $step2->id]);
        $step2->update(['next_step' => $step3->id]);
        $step3->update(['next_step' => $step4->id]);
        $step4->update(['next_step' => $step5->id]);

        $this->info("✅ Created 5 steps");

        // Create components and buttons for interactive steps
        $this->createWelcomeButtons($step1);
        $this->createSizeButtons($step2);
        $this->createConfirmationButtons($step4);

        $this->info("✅ Created interactive components and buttons");

        // Update flow steps count
        $flow->update(['steps_count' => 5]);

        $this->info("🎉 Pizza Delivery Flow created successfully!");
        $this->info("Flow ID: {$flow->id}");
        $this->info("You can now assign this flow to a phone number.");

        return 0;
    }

    private function createWelcomeButtons(Step $step)
    {
        $component = Component::create([
            'organization_id' => $step->organization_id,
            'step_id' => $step->id,
            'name' => 'Welcome Buttons',
            'type' => 'BUTTONS',
            'format' => 'TEXT',
            'text' => 'Welcome to Pizza Express! 🍕\n\nWhat would you like to do today?',
            'json' => json_encode(['button_type' => 'quick_reply'])
        ]);

        $buttons = [
            ['text' => '🍕 Order Pizza', 'callback_data' => 'order_pizza'],
            ['text' => '📋 View Menu', 'callback_data' => 'view_menu'],
            ['text' => '📞 Contact Us', 'callback_data' => 'contact_us']
        ];

        foreach ($buttons as $buttonData) {
            $button = Button::create([
                'organization_id' => $step->organization_id,
                'text' => $buttonData['text'],
                'type' => 'QUICK_REPLY',
                'callback_data' => $buttonData['callback_data'],
                'json' => json_encode(['action' => $buttonData['callback_data']])
            ]);

            $component->buttons()->attach($button->id);
        }
    }

    private function createSizeButtons(Step $step)
    {
        $component = Component::create([
            'organization_id' => $step->organization_id,
            'step_id' => $step->id,
            'name' => 'Size Selection',
            'type' => 'BUTTONS',
            'format' => 'TEXT',
            'text' => 'Great! Please choose your pizza size:',
            'json' => json_encode(['button_type' => 'quick_reply'])
        ]);

        $buttons = [
            ['text' => '🍕 Small - $12.99', 'callback_data' => 'size_small'],
            ['text' => '🍕 Medium - $16.99', 'callback_data' => 'size_medium'],
            ['text' => '🍕 Large - $20.99', 'callback_data' => 'size_large']
        ];

        foreach ($buttons as $buttonData) {
            $button = Button::create([
                'organization_id' => $step->organization_id,
                'text' => $buttonData['text'],
                'type' => 'QUICK_REPLY',
                'callback_data' => $buttonData['callback_data'],
                'json' => json_encode(['action' => $buttonData['callback_data']])
            ]);

            $component->buttons()->attach($button->id);
        }
    }

    private function createConfirmationButtons(Step $step)
    {
        $component = Component::create([
            'organization_id' => $step->organization_id,
            'step_id' => $step->id,
            'name' => 'Order Confirmation',
            'type' => 'BUTTONS',
            'format' => 'TEXT',
            'text' => 'Please confirm your order:',
            'json' => json_encode(['button_type' => 'quick_reply'])
        ]);

        $buttons = [
            ['text' => '✅ Confirm Order', 'callback_data' => 'confirm_order'],
            ['text' => '❌ Cancel Order', 'callback_data' => 'cancel_order'],
            ['text' => '🔄 Modify Order', 'callback_data' => 'modify_order']
        ];

        foreach ($buttons as $buttonData) {
            $button = Button::create([
                'organization_id' => $step->organization_id,
                'text' => $buttonData['text'],
                'type' => 'QUICK_REPLY',
                'callback_data' => $buttonData['callback_data'],
                'json' => json_encode(['action' => $buttonData['callback_data']])
            ]);

            $component->buttons()->attach($button->id);
        }
    }
}

<?php

namespace App\Http\Requests\PhoneNumber;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'user_id' => 'nullable|exists:users,id',
            'client_id' => 'nullable|exists:clients,id',
            'phone_number' => 'nullable|string',
            'name' => 'nullable|string',
            'description' => 'nullable|string',
            'is_active' => 'nullable|boolean'
        ];
    }

    public function messages()
    {
        return [
            'user_id.exists' => __('The selected user does not exist.'),
            'client_id.exists' => __('The selected client does not exist.'),
        ];
    }
}
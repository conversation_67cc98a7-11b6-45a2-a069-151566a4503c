<?php

namespace App\Http\Requests\User;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'email' => 'required|unique:users',
            'profile_id' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'username' => 'required|unique:users',
            'password' => 'confirmed|min:6',
        ];
    }

    public function messages()
    {
        return [
            'email.required' => __('The email field is required.'),
            'profile_id.required' => __('The profile field is required.'),
            'email.unique' => __('This email is already registered.'),
            'first_name.required' => __('The first name field is required.'),
            'last_name.required' => __('The last name field is required.'),
            'username.required' => __('The username field is required.'),
            'username.unique' => __('This username is already registered.'),
            'password.required' => __('The password field is required.'),
            'password.confirmed' => __('The passwords do not match.'),
            'password.min' => __('The passwords need at least 6 characters.'),
        ];
    }
}

<?php

namespace App\Http\Controllers\Meta\WhatsApp;

use App\Http\Controllers\Controller;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\UseCases\ChatBot\PhoneNumber\Get;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Helpers\Traits\Response;

class WhatsAppWebhookController extends Controller
{
    use Response;

    protected ChatBotService $chatBotService;

    public function __construct(ChatBotService $chatBotService)
    {
        $this->chatBotService = $chatBotService;
    }

    /**
     * Handle WhatsApp webhook verification (GET request)
     */
    public function verify(Request $request): JsonResponse
    {
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        // Verify the webhook
        $verifyToken = config('whatsapp.webhook_verify_token', 'your_verify_token');

        if ($mode === 'subscribe' && $token === $verifyToken) {
            Log::info('WhatsApp webhook verified successfully');
            return response()->json((int) $challenge);
        }

        Log::warning('WhatsApp webhook verification failed', [
            'mode' => $mode,
            'token' => $token,
            'expected_token' => $verifyToken
        ]);

        return response()->json(['error' => 'Forbidden'], 403);
    }

    /**
     * Handle WhatsApp webhook messages (POST request)
     */
    public function receiveMessage(Request $request): JsonResponse
    {
        try {
            $webhookData = $request->all();

            Log::info('WhatsApp webhook received', ['data' => $webhookData]);

            // Validate webhook structure
            if (!$this->isValidWebhookData($webhookData)) {
                Log::warning('Invalid WhatsApp webhook data structure', ['data' => $webhookData]);
                return response()->json(['status' => 'error', 'message' => 'Invalid webhook data'], 400);
            }

            // Process each entry in the webhook
            $results = [];
            foreach ($webhookData['entry'] ?? [] as $entry) {
                foreach ($entry['changes'] ?? [] as $change) {
                    if ($change['field'] === 'messages') {
                        $result = $this->processMessageChange($change['value']);
                        if ($result) {
                            $results[] = $result;
                        }
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'processed' => count($results),
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('WhatsApp webhook processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $request->all()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Process a message change from webhook
     */
    protected function processMessageChange(array $changeValue): ?array
    {
        try {
            // Skip status updates, only process incoming messages
            if (!isset($changeValue['messages']) || empty($changeValue['messages'])) {
                return null;
            }

            // Process each message
            foreach ($changeValue['messages'] as $message) {
                // Skip outgoing messages (from business)
                if (isset($message['from']) && $this->isBusinessNumber($message['from'])) {
                    continue;
                }

                // Process the message through ChatBot service
                $result = $this->chatBotService->processWebhook([
                    'message' => $message,
                    'metadata' => $changeValue['metadata'] ?? [],
                    'contacts' => $changeValue['contacts'] ?? []
                ]);

                return $result;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Error processing message change', [
                'error' => $e->getMessage(),
                'change_value' => $changeValue
            ]);

            throw $e;
        }
    }

    /**
     * Validate webhook data structure
     */
    protected function isValidWebhookData(array $data): bool
    {
        return isset($data['object']) &&
               $data['object'] === 'whatsapp_business_account' &&
               isset($data['entry']) &&
               is_array($data['entry']);
    }

    /**
     * Check if phone number belongs to business (to skip outgoing messages)
     */
    protected function isBusinessNumber(string $phoneNumber): bool
    {
        // TODO: Implement logic to check if phone number is a business number
        // For now, we'll assume all messages are incoming
        return false;
    }

    public function testWhatsAppToken(int $id)
    {
        /** @var Get $useCase */
        $useCase = app()->make(Get::class);
        $phoneNumber = $useCase->perform($id);

        $accessToken = $phoneNumber->whatsapp_access_token;
        $phoneNumberId = $phoneNumber->whatsapp_phone_number_id;

        $url = "https://graph.facebook.com/v19.0/{$phoneNumberId}/whatsapp_business_profile";

        $response = Http::withToken(
            $accessToken
        )->get($url);

        if ($response->successful()) {
            return $this->response(
                "Whatsapp phone number is fully integrated",
                "success",
                200,
                $response->json()
            );
        } else {
            $response = Http::withToken($accessToken)->get('https://graph.facebook.com/v19.0/me');
            if (!$response->successful()) {
                return $this->response(
                    "Whatsapp phone number is not fully integrated even with me: " . $url,
                    "error",
                    401,
                    $response->json()
                );
            }
            return $this->response(
                "Whatsapp phone number is not fully integrated: " . $url,
                "error",
                401,
                $response->json()
            );
        }
    }
}

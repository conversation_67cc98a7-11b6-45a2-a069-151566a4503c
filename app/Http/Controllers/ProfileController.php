<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\Response;
use App\UseCases\Profile\Get;
use App\UseCases\Profile\GetAll;
use Illuminate\Http\JsonResponse;

class ProfileController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $data = $useCase->perform();

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store() : JsonResponse {
        try{
            return $this->response("endpoint unavailable");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update() : JsonResponse {
        try{
            return $this->response("endpoint unavailable");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy() : JsonResponse {
        try{
            return $this->response("endpoint unavailable");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
